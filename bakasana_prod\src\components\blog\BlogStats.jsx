'use client';

import React from 'react';

export default function BlogStats({ posts }) {
  const totalPosts = posts.length;
  const categories = [...new Set(posts.map(post => post.category).filter(Boolean))];
  const totalReadTime = posts.reduce((total, post) => total + (parseInt(post.readTime) || 5), 0);
  
  const stats = [
    {
      label: 'Artykułów',
      value: totalPosts,
      icon: '📝'
    },
    {
      label: 'Kategorii',
      value: categories.length,
      icon: '🏷️'
    },
    {
      label: 'Minut czytania',
      value: totalReadTime,
      icon: '⏱️'
    },
    {
      label: 'Krajów',
      value: '2+',
      icon: '🌏'
    }
  ];

  return (
    <div className="bg-white/50 backdrop-blur-sm py-8">
      <div className="max-w-6xl mx-auto px-hero-padding">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-cormorant text-charcoal font-light">
                {stat.value}
              </div>
              <div className="text-sm text-charcoal/70 font-light">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
