[{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js": "1", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js": "2", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js": "3", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js": "4", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js": "5", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js": "6", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\fitssey\\webhook\\route.js": "7", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js": "8", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\metadata.js": "9", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js": "10", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js": "11", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js": "12", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js": "13", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\metadata.js": "14", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js": "16", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js": "17", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js": "18", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js": "19", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js": "20", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js": "21", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\metadata.js": "22", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx": "24", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx": "26", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js": "27", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx": "28", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js": "30", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js": "31", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js": "32", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js": "33", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js": "34", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js": "35", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js": "36", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js": "37", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useFormValidation.js": "38", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js": "39", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js": "40", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js": "41", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js": "42", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js": "43", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js": "44", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js": "45", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware-redirects.js": "46", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\errorHandler.js": "47"}, {"size": 6440, "mtime": 1750136629000, "results": "48", "hashOfConfig": "49"}, {"size": 4713, "mtime": 1753306423169, "results": "50", "hashOfConfig": "49"}, {"size": 5821, "mtime": 1753306423170, "results": "51", "hashOfConfig": "49"}, {"size": 4277, "mtime": 1753306423167, "results": "52", "hashOfConfig": "49"}, {"size": 2317, "mtime": 1753306423165, "results": "53", "hashOfConfig": "49"}, {"size": 7554, "mtime": 1753311000278, "results": "54", "hashOfConfig": "49"}, {"size": 6783, "mtime": 1753306423161, "results": "55", "hashOfConfig": "49"}, {"size": 2387, "mtime": 1753306423160, "results": "56", "hashOfConfig": "49"}, {"size": 1831, "mtime": 1753303437736, "results": "57", "hashOfConfig": "49"}, {"size": 1620, "mtime": 1753306423158, "results": "58", "hashOfConfig": "49"}, {"size": 1885, "mtime": 1753303467945, "results": "59", "hashOfConfig": "49"}, {"size": 1875, "mtime": 1753303484749, "results": "60", "hashOfConfig": "49"}, {"size": 4688, "mtime": 1752410773610, "results": "61", "hashOfConfig": "49"}, {"size": 1838, "mtime": 1753303426524, "results": "62", "hashOfConfig": "49"}, {"size": 968, "mtime": 1753310852493, "results": "63", "hashOfConfig": "64"}, {"size": 3811, "mtime": 1752534114385, "results": "65", "hashOfConfig": "49"}, {"size": 2925, "mtime": 1753303781126, "results": "66", "hashOfConfig": "49"}, {"size": 2000, "mtime": 1752534592254, "results": "67", "hashOfConfig": "49"}, {"size": 2160, "mtime": 1752661449491, "results": "68", "hashOfConfig": "49"}, {"size": 7060, "mtime": 1753306423129, "results": "69", "hashOfConfig": "49"}, {"size": 1334, "mtime": 1753303785295, "results": "70", "hashOfConfig": "49"}, {"size": 1722, "mtime": 1753304387578, "results": "71", "hashOfConfig": "49"}, {"size": 5601, "mtime": 1753308299847, "results": "72", "hashOfConfig": "64"}, {"size": 11219, "mtime": 1753308299845, "results": "73", "hashOfConfig": "64"}, {"size": 9098, "mtime": 1753308299844, "results": "74", "hashOfConfig": "64"}, {"size": 6109, "mtime": 1753308299843, "results": "75", "hashOfConfig": "64"}, {"size": 13396, "mtime": 1752509953353, "results": "76", "hashOfConfig": "49"}, {"size": 8250, "mtime": 1753308299841, "results": "77", "hashOfConfig": "64"}, {"size": 2231, "mtime": 1753310870375, "results": "78", "hashOfConfig": "49"}, {"size": 1189, "mtime": 1753306423126, "results": "79", "hashOfConfig": "49"}, {"size": 17092, "mtime": 1753306423124, "results": "80", "hashOfConfig": "49"}, {"size": 1121, "mtime": 1753306423123, "results": "81", "hashOfConfig": "49"}, {"size": 1901, "mtime": 1753306423122, "results": "82", "hashOfConfig": "49"}, {"size": 1954, "mtime": 1753304401990, "results": "83", "hashOfConfig": "49"}, {"size": 11592, "mtime": 1752669072282, "results": "84", "hashOfConfig": "49"}, {"size": 18399, "mtime": 1753176491020, "results": "85", "hashOfConfig": "49"}, {"size": 11008, "mtime": 1753306423119, "results": "86", "hashOfConfig": "49"}, {"size": 6758, "mtime": 1753306423117, "results": "87", "hashOfConfig": "49"}, {"size": 1990, "mtime": 1753306423115, "results": "88", "hashOfConfig": "49"}, {"size": 15647, "mtime": 1752532937438, "results": "89", "hashOfConfig": "49"}, {"size": 19639, "mtime": 1752789179363, "results": "90", "hashOfConfig": "49"}, {"size": 10182, "mtime": 1750224902000, "results": "91", "hashOfConfig": "49"}, {"size": 2224, "mtime": 1753306423111, "results": "92", "hashOfConfig": "49"}, {"size": 4983, "mtime": 1752413498649, "results": "93", "hashOfConfig": "49"}, {"size": 2853, "mtime": 1753306423109, "results": "94", "hashOfConfig": "49"}, {"size": 1406, "mtime": 1753305546521, "results": "95", "hashOfConfig": "49"}, {"size": 8722, "mtime": 1753310946788, "results": "96", "hashOfConfig": "49"}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lcw2q5", {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19x6zoz", {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\fitssey\\webhook\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useFormValidation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware-redirects.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\errorHandler.js", [], []]