"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":
/*!************************************************!*\
  !*** ./src/app/blog/BlogPageClientContent.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPageClientContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Magazine Style Blog Card\nconst PostCard = (param)=>{\n    let { post, featured = false, className = '' } = param;\n    if (!post) return null;\n    const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"\".concat(cardClass, \" \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: \"/blog/\".concat(post.slug || '#'),\n            className: \"magazine-card-link\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-image-bg\",\n                        style: {\n                            backgroundImage: \"url(\".concat(post.imageUrl || '/images/placeholder/image.jpg', \")\"),\n                            backgroundSize: 'cover',\n                            backgroundPosition: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-image-overlay\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"magazine-category\",\n                                children: post.category || 'Zapiski z podróży'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"magazine-card-title\",\n                            children: post.title || 'Bez tytułu'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-card-excerpt\",\n                            children: post.excerpt || ''\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-card-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-more\",\n                                    children: \"Czytaj więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                post.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-time\",\n                                    children: [\n                                        post.readTime,\n                                        \" min\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PostCard;\nfunction BlogPageClientContent(param) {\n    let { posts = [] } = param;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('wszystkie');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const memoizedPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[memoizedPosts]\": ()=>{\n            if (!Array.isArray(posts)) return [];\n            return posts.filter({\n                \"BlogPageClientContent.useMemo[memoizedPosts]\": (post)=>post && typeof post === 'object'\n            }[\"BlogPageClientContent.useMemo[memoizedPosts]\"]);\n        }\n    }[\"BlogPageClientContent.useMemo[memoizedPosts]\"], [\n        posts\n    ]);\n    // Get unique categories\n    const categories = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[categories]\": ()=>{\n            const cats = [\n                'wszystkie',\n                ...new Set(memoizedPosts.map({\n                    \"BlogPageClientContent.useMemo[categories]\": (post)=>post.category\n                }[\"BlogPageClientContent.useMemo[categories]\"]).filter(Boolean))\n            ];\n            return cats;\n        }\n    }[\"BlogPageClientContent.useMemo[categories]\"], [\n        memoizedPosts\n    ]);\n    // Filter posts based on category and search\n    const filteredPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[filteredPosts]\": ()=>{\n            let filtered = memoizedPosts;\n            if (selectedCategory !== 'wszystkie') {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>post.category === selectedCategory\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            if (searchTerm) {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>{\n                        var _post_title, _post_excerpt, _post_tags;\n                        return ((_post_title = post.title) === null || _post_title === void 0 ? void 0 : _post_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_excerpt = post.excerpt) === null || _post_excerpt === void 0 ? void 0 : _post_excerpt.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_tags = post.tags) === null || _post_tags === void 0 ? void 0 : _post_tags.some({\n                            \"BlogPageClientContent.useMemo[filteredPosts]\": (tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())\n                        }[\"BlogPageClientContent.useMemo[filteredPosts]\"]));\n                    }\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            return filtered;\n        }\n    }[\"BlogPageClientContent.useMemo[filteredPosts]\"], [\n        memoizedPosts,\n        selectedCategory,\n        searchTerm\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-sanctuary min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-hero\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"magazine-title\",\n                            children: \"Zapiski z Podr\\xf3ży\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-subtitle\",\n                            children: \"Historie napisane sercem • Inspiracje z Azji • Praktyka jogi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 max-w-md mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Szukaj artykuł\\xf3w...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 rounded-none text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-meta\",\n                            children: [\n                                filteredPosts.length,\n                                \" \",\n                                filteredPosts.length === 1 ? 'artykuł' : 'artykułów'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 bg-white/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-hero-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-3\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-2 text-sm font-light tracking-wide transition-all duration-300 \".concat(selectedCategory === category ? 'bg-enterprise-brown text-white' : 'bg-white/80 text-charcoal hover:bg-enterprise-brown/10 border border-enterprise-brown/20'),\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-content\",\n                children: filteredPosts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        filteredPosts[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-hero-padding mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-2 lg:order-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-enterprise-brown/10 text-enterprise-brown text-xs font-medium tracking-wide uppercase\",\n                                                            children: \"Wyr\\xf3żniony\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-charcoal/60 text-sm\",\n                                                            children: filteredPosts[0].category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-4xl lg:text-5xl font-cormorant text-charcoal leading-tight\",\n                                                    children: filteredPosts[0].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-charcoal/80 leading-relaxed\",\n                                                    children: filteredPosts[0].excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-sm text-charcoal/60\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: filteredPosts[0].author\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                filteredPosts[0].readTime || '5',\n                                                                \" min\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: new Date(filteredPosts[0].date).toLocaleDateString('pl-PL')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/blog/\".concat(filteredPosts[0].slug),\n                                                    className: \"inline-flex items-center gap-2 text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300 font-medium\",\n                                                    children: [\n                                                        \"Czytaj artykuł\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: \"→\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-1 lg:order-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/3] relative overflow-hidden bg-charcoal/5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: filteredPosts[0].imageUrl || '/images/placeholder/image.jpg',\n                                                alt: filteredPosts[0].imageAlt || filteredPosts[0].title,\n                                                fill: true,\n                                                className: \"object-cover transition-transform duration-700 hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 152,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-hero-padding\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: filteredPosts.slice(1).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/blog/\".concat(post.slug),\n                                            className: \"block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-[16/10] relative overflow-hidden bg-charcoal/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: post.imageUrl || '/images/placeholder/image.jpg',\n                                                                alt: post.imageAlt || post.title,\n                                                                fill: true,\n                                                                className: \"object-cover transition-transform duration-500 group-hover:scale-105\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-white/90 backdrop-blur-sm text-charcoal text-xs font-medium\",\n                                                                    children: post.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-cormorant text-charcoal leading-tight group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: post.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-charcoal/70 text-sm leading-relaxed line-clamp-3\",\n                                                                children: post.excerpt\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-charcoal/60\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(post.date).toLocaleDateString('pl-PL')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            post.readTime || '5',\n                                                                            \" min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, post.slug || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-hero-padding py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-cormorant text-charcoal\",\n                                children: searchTerm ? 'Nie znaleziono artykułów' : 'Wkrótce więcej treści'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-charcoal/70\",\n                                children: searchTerm ? 'Brak wynik\\xf3w dla \"'.concat(searchTerm, '\". Spr\\xf3buj innych sł\\xf3w kluczowych.') : 'Pracujemy nad nowymi inspirującymi artykułami'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this),\n                            searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSearchTerm(''),\n                                className: \"text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300\",\n                                children: \"Wyczyść wyszukiwanie\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-lg max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-header\",\n                                    children: \"Bądź na bieżąco\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-text opacity-80\",\n                                    children: \"Otrzymuj najnowsze artykuły i inspiracje z duchowych podr\\xf3ży\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center my-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-sm text-charcoal-gold/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg opacity-60\",\n                                        children: \"ॐ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-lg justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.instagram.com/fly_with_bakasana\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-ghost\",\n                                    children: \"Instagram\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"btn-ghost\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-stone font-light italic tracking-wide\",\n                                    children: '\"Każda historia ma swoją moc...\"'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2\",\n                                    children: \"Om Swastiastu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPageClientContent, \"lo2uijBVUg9EXKtYRzlq39aa0Ko=\");\n_c1 = BlogPageClientContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"PostCard\");\n$RefreshReg$(_c1, \"BlogPageClientContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx\n"));

/***/ })

});