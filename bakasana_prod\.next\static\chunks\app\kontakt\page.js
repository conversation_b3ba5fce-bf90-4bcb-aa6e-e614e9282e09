/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/kontakt/page"],{

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e) {\n    var t, f, n = \"\";\n    if (\"string\" == typeof e || \"number\" == typeof e) n += e;\n    else if (\"object\" == typeof e) if (Array.isArray(e)) {\n        var o = e.length;\n        for(t = 0; t < o; t++)e[t] && (f = r(e[t])) && (n && (n += \" \"), n += f);\n    } else for(f in e)e[f] && (n && (n += \" \"), n += f);\n    return n;\n}\nfunction clsx() {\n    for(var e, t, f = 0, n = \"\", o = arguments.length; f < o; f++)(e = arguments[f]) && (t = r(e)) && (n && (n += \" \"), n += t);\n    return n;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxTQUFTQSxFQUFFQyxDQUFDO0lBQUUsSUFBSUMsR0FBRUMsR0FBRUMsSUFBRTtJQUFHLElBQUcsWUFBVSxPQUFPSCxLQUFHLFlBQVUsT0FBT0EsR0FBRUcsS0FBR0g7U0FBTyxJQUFHLFlBQVUsT0FBT0EsR0FBRSxJQUFHSSxNQUFNQyxPQUFPLENBQUNMLElBQUc7UUFBQyxJQUFJTSxJQUFFTixFQUFFTyxNQUFNO1FBQUMsSUFBSU4sSUFBRSxHQUFFQSxJQUFFSyxHQUFFTCxJQUFJRCxDQUFDLENBQUNDLEVBQUUsSUFBR0MsQ0FBQUEsSUFBRUgsRUFBRUMsQ0FBQyxDQUFDQyxFQUFFLE1BQUtFLENBQUFBLEtBQUlBLENBQUFBLEtBQUcsR0FBRSxHQUFHQSxLQUFHRCxDQUFBQTtJQUFFLE9BQU0sSUFBSUEsS0FBS0YsRUFBRUEsQ0FBQyxDQUFDRSxFQUFFLElBQUdDLENBQUFBLEtBQUlBLENBQUFBLEtBQUcsR0FBRSxHQUFHQSxLQUFHRCxDQUFBQTtJQUFHLE9BQU9DO0FBQUM7QUFBUSxTQUFTSztJQUFPLElBQUksSUFBSVIsR0FBRUMsR0FBRUMsSUFBRSxHQUFFQyxJQUFFLElBQUdHLElBQUVHLFVBQVVGLE1BQU0sRUFBQ0wsSUFBRUksR0FBRUosSUFBSSxDQUFDRixJQUFFUyxTQUFTLENBQUNQLEVBQUUsS0FBSUQsQ0FBQUEsSUFBRUYsRUFBRUMsRUFBQyxLQUFLRyxDQUFBQSxLQUFJQSxDQUFBQSxLQUFHLEdBQUUsR0FBR0EsS0FBR0YsQ0FBQUE7SUFBRyxPQUFPRTtBQUFDO0FBQUMsaUVBQWVLLElBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbInIiLCJlIiwidCIsImYiLCJuIiwiQXJyYXkiLCJpc0FycmF5IiwibyIsImxlbmd0aCIsImNsc3giLCJhcmd1bWVudHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kontakt/ContactForm.jsx */ \"(app-pages-browser)/./src/app/kontakt/ContactForm.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformantWhatsApp.jsx */ \"(app-pages-browser)/./src/components/PerformantWhatsApp.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZGF2aWQlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVrdHklNUMlNUNiYWthc2FuYV9wcm9kJTVDJTVDYmFrYXNhbmFfcHJvZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tvbnRha3QlNUMlNUNDb250YWN0Rm9ybS5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmFrYXNhbmFfcHJvZCU1QyU1Q2Jha2FzYW5hX3Byb2QlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUGVyZm9ybWFudFdoYXRzQXBwLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBdUs7QUFDdks7QUFDQSx3TUFBNEsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJha2FzYW5hX3Byb2RcXFxcYmFrYXNhbmFfcHJvZFxcXFxzcmNcXFxcYXBwXFxcXGtvbnRha3RcXFxcQ29udGFjdEZvcm0uanN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZGF2aWRcXFxcRGVza3RvcFxcXFxQcm9qZWt0eVxcXFxiYWthc2FuYV9wcm9kXFxcXGJha2FzYW5hX3Byb2RcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUGVyZm9ybWFudFdoYXRzQXBwLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = (config)=>{\n    const classMap = createClassMap(config);\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config;\n    const getClassGroupId = (className)=>{\n        const classParts = className.split(CLASS_PART_SEPARATOR);\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift();\n        }\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n    };\n    const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier)=>{\n        const conflicts = conflictingClassGroups[classGroupId] || [];\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [\n                ...conflicts,\n                ...conflictingClassGroupModifiers[classGroupId]\n            ];\n        }\n        return conflicts;\n    };\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds\n    };\n};\nconst getGroupRecursive = (classParts, classPartObject)=>{\n    var _classPartObject_validators_find;\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId;\n    }\n    const currentClassPart = classParts[0];\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n    const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart;\n    }\n    if (classPartObject.validators.length === 0) {\n        return undefined;\n    }\n    const classRest = classParts.join(CLASS_PART_SEPARATOR);\n    return (_classPartObject_validators_find = classPartObject.validators.find((param)=>{\n        let { validator } = param;\n        return validator(classRest);\n    })) === null || _classPartObject_validators_find === void 0 ? void 0 : _classPartObject_validators_find.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = (className)=>{\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n        const property = arbitraryPropertyClassName === null || arbitraryPropertyClassName === void 0 ? void 0 : arbitraryPropertyClassName.substring(0, arbitraryPropertyClassName.indexOf(':'));\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property;\n        }\n    }\n};\n/**\n * Exported for testing only\n */ const createClassMap = (config)=>{\n    const { theme, classGroups } = config;\n    const classMap = {\n        nextPart: new Map(),\n        validators: []\n    };\n    for(const classGroupId in classGroups){\n        processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n    }\n    return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme)=>{\n    classGroup.forEach((classDefinition)=>{\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n            classPartObjectToEdit.classGroupId = classGroupId;\n            return;\n        }\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n                return;\n            }\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId\n            });\n            return;\n        }\n        Object.entries(classDefinition).forEach((param)=>{\n            let [key, classGroup] = param;\n            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n        });\n    });\n};\nconst getPart = (classPartObject, path)=>{\n    let currentClassPartObject = classPartObject;\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart)=>{\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: []\n            });\n        }\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n    });\n    return currentClassPartObject;\n};\nconst isThemeGetter = (func)=>func.isThemeGetter;\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = (maxCacheSize)=>{\n    if (maxCacheSize < 1) {\n        return {\n            get: ()=>undefined,\n            set: ()=>{}\n        };\n    }\n    let cacheSize = 0;\n    let cache = new Map();\n    let previousCache = new Map();\n    const update = (key, value)=>{\n        cache.set(key, value);\n        cacheSize++;\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0;\n            previousCache = cache;\n            cache = new Map();\n        }\n    };\n    return {\n        get (key) {\n            let value = cache.get(key);\n            if (value !== undefined) {\n                return value;\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value);\n                return value;\n            }\n        },\n        set (key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value);\n            } else {\n                update(key, value);\n            }\n        }\n    };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = (config)=>{\n    const { prefix, experimentalParseClassName } = config;\n    /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */ let parseClassName = (className)=>{\n        const modifiers = [];\n        let bracketDepth = 0;\n        let parenDepth = 0;\n        let modifierStart = 0;\n        let postfixModifierPosition;\n        for(let index = 0; index < className.length; index++){\n            let currentCharacter = className[index];\n            if (bracketDepth === 0 && parenDepth === 0) {\n                if (currentCharacter === MODIFIER_SEPARATOR) {\n                    modifiers.push(className.slice(modifierStart, index));\n                    modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n                    continue;\n                }\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index;\n                    continue;\n                }\n            }\n            if (currentCharacter === '[') {\n                bracketDepth++;\n            } else if (currentCharacter === ']') {\n                bracketDepth--;\n            } else if (currentCharacter === '(') {\n                parenDepth++;\n            } else if (currentCharacter === ')') {\n                parenDepth--;\n            }\n        }\n        const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n        const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n        const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n        const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition\n        };\n    };\n    if (prefix) {\n        const fullPrefix = prefix + MODIFIER_SEPARATOR;\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n                isExternal: true,\n                modifiers: [],\n                hasImportantModifier: false,\n                baseClassName: className,\n                maybePostfixModifierPosition: undefined\n            };\n    }\n    if (experimentalParseClassName) {\n        const parseClassNameOriginal = parseClassName;\n        parseClassName = (className)=>experimentalParseClassName({\n                className,\n                parseClassName: parseClassNameOriginal\n            });\n    }\n    return parseClassName;\n};\nconst stripImportantModifier = (baseClassName)=>{\n    if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(0, baseClassName.length - 1);\n    }\n    /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */ if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n        return baseClassName.substring(1);\n    }\n    return baseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */ const createSortModifiers = (config)=>{\n    const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map((modifier)=>[\n            modifier,\n            true\n        ]));\n    const sortModifiers = (modifiers)=>{\n        if (modifiers.length <= 1) {\n            return modifiers;\n        }\n        const sortedModifiers = [];\n        let unsortedModifiers = [];\n        modifiers.forEach((modifier)=>{\n            const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n            if (isPositionSensitive) {\n                sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n                unsortedModifiers = [];\n            } else {\n                unsortedModifiers.push(modifier);\n            }\n        });\n        sortedModifiers.push(...unsortedModifiers.sort());\n        return sortedModifiers;\n    };\n    return sortModifiers;\n};\nconst createConfigUtils = (config)=>({\n        cache: createLruCache(config.cacheSize),\n        parseClassName: createParseClassName(config),\n        sortModifiers: createSortModifiers(config),\n        ...createClassGroupUtils(config)\n    });\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils)=>{\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds, sortModifiers } = configUtils;\n    /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */ const classGroupsInConflict = [];\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n    let result = '';\n    for(let index = classNames.length - 1; index >= 0; index -= 1){\n        const originalClassName = classNames[index];\n        const { isExternal, modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } = parseClassName(originalClassName);\n        if (isExternal) {\n            result = originalClassName + (result.length > 0 ? ' ' + result : result);\n            continue;\n        }\n        let hasPostfixModifier = !!maybePostfixModifierPosition;\n        let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result);\n                continue;\n            }\n            classGroupId = getClassGroupId(baseClassName);\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result);\n                continue;\n            }\n            hasPostfixModifier = false;\n        }\n        const variantModifier = sortModifiers(modifiers).join(':');\n        const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n        const classId = modifierId + classGroupId;\n        if (classGroupsInConflict.includes(classId)) {\n            continue;\n        }\n        classGroupsInConflict.push(classId);\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n        for(let i = 0; i < conflictGroups.length; ++i){\n            const group = conflictGroups[i];\n            classGroupsInConflict.push(modifierId + group);\n        }\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n    }\n    return result;\n};\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */ function twJoin() {\n    let index = 0;\n    let argument;\n    let resolvedValue;\n    let string = '';\n    while(index < arguments.length){\n        if (argument = arguments[index++]) {\n            if (resolvedValue = toValue(argument)) {\n                string && (string += ' ');\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nconst toValue = (mix)=>{\n    if (typeof mix === 'string') {\n        return mix;\n    }\n    let resolvedValue;\n    let string = '';\n    for(let k = 0; k < mix.length; k++){\n        if (mix[k]) {\n            if (resolvedValue = toValue(mix[k])) {\n                string && (string += ' ');\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n};\nfunction createTailwindMerge(createConfigFirst) {\n    for(var _len = arguments.length, createConfigRest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        createConfigRest[_key - 1] = arguments[_key];\n    }\n    let configUtils;\n    let cacheGet;\n    let cacheSet;\n    let functionToCall = initTailwindMerge;\n    function initTailwindMerge(classList) {\n        const config = createConfigRest.reduce((previousConfig, createConfigCurrent)=>createConfigCurrent(previousConfig), createConfigFirst());\n        configUtils = createConfigUtils(config);\n        cacheGet = configUtils.cache.get;\n        cacheSet = configUtils.cache.set;\n        functionToCall = tailwindMerge;\n        return tailwindMerge(classList);\n    }\n    function tailwindMerge(classList) {\n        const cachedResult = cacheGet(classList);\n        if (cachedResult) {\n            return cachedResult;\n        }\n        const result = mergeClassList(classList, configUtils);\n        cacheSet(classList, result);\n        return result;\n    }\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments));\n    };\n}\nconst fromTheme = (key)=>{\n    const themeGetter = (theme)=>theme[key] || [];\n    themeGetter.isThemeGetter = true;\n    return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = (value)=>fractionRegex.test(value);\nconst isNumber = (value)=>!!value && !Number.isNaN(Number(value));\nconst isInteger = (value)=>!!value && Number.isInteger(Number(value));\nconst isPercent = (value)=>value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = (value)=>tshirtUnitRegex.test(value);\nconst isAny = ()=>true;\nconst isLengthOnly = (value)=>// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = ()=>false;\nconst isShadow = (value)=>shadowRegex.test(value);\nconst isImage = (value)=>imageRegex.test(value);\nconst isAnyNonArbitrary = (value)=>!isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = (value)=>getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = (value)=>arbitraryValueRegex.test(value);\nconst isArbitraryLength = (value)=>getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = (value)=>getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = (value)=>getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = (value)=>getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = (value)=>getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = (value)=>arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = (value)=>getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = (value)=>getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = (value)=>getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = (value)=>getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = (value)=>getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = (value)=>getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue)=>{\n    const result = arbitraryValueRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return testValue(result[2]);\n    }\n    return false;\n};\nconst getIsArbitraryVariable = function(value, testLabel) {\n    let shouldMatchNoLabel = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    const result = arbitraryVariableRegex.exec(value);\n    if (result) {\n        if (result[1]) {\n            return testLabel(result[1]);\n        }\n        return shouldMatchNoLabel;\n    }\n    return false;\n};\n// Labels\nconst isLabelPosition = (label)=>label === 'position' || label === 'percentage';\nconst isLabelImage = (label)=>label === 'image' || label === 'url';\nconst isLabelSize = (label)=>label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = (label)=>label === 'length';\nconst isLabelNumber = (label)=>label === 'number';\nconst isLabelFamilyName = (label)=>label === 'family-name';\nconst isLabelShadow = (label)=>label === 'shadow';\nconst validators = /*#__PURE__*/ Object.defineProperty({\n    __proto__: null,\n    isAny,\n    isAnyNonArbitrary,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isArbitraryVariable,\n    isArbitraryVariableFamilyName,\n    isArbitraryVariableImage,\n    isArbitraryVariableLength,\n    isArbitraryVariablePosition,\n    isArbitraryVariableShadow,\n    isArbitraryVariableSize,\n    isFraction,\n    isInteger,\n    isNumber,\n    isPercent,\n    isTshirtSize\n}, Symbol.toStringTag, {\n    value: 'Module'\n});\nconst getDefaultConfig = ()=>{\n    /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */ /***/ const themeColor = fromTheme('color');\n    const themeFont = fromTheme('font');\n    const themeText = fromTheme('text');\n    const themeFontWeight = fromTheme('font-weight');\n    const themeTracking = fromTheme('tracking');\n    const themeLeading = fromTheme('leading');\n    const themeBreakpoint = fromTheme('breakpoint');\n    const themeContainer = fromTheme('container');\n    const themeSpacing = fromTheme('spacing');\n    const themeRadius = fromTheme('radius');\n    const themeShadow = fromTheme('shadow');\n    const themeInsetShadow = fromTheme('inset-shadow');\n    const themeTextShadow = fromTheme('text-shadow');\n    const themeDropShadow = fromTheme('drop-shadow');\n    const themeBlur = fromTheme('blur');\n    const themePerspective = fromTheme('perspective');\n    const themeAspect = fromTheme('aspect');\n    const themeEase = fromTheme('ease');\n    const themeAnimate = fromTheme('animate');\n    /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */ /***/ const scaleBreak = ()=>[\n            'auto',\n            'avoid',\n            'all',\n            'avoid-page',\n            'page',\n            'left',\n            'right',\n            'column'\n        ];\n    const scalePosition = ()=>[\n            'center',\n            'top',\n            'bottom',\n            'left',\n            'right',\n            'top-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-top',\n            'top-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-top',\n            'bottom-right',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'right-bottom',\n            'bottom-left',\n            // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n            'left-bottom'\n        ];\n    const scalePositionWithArbitrary = ()=>[\n            ...scalePosition(),\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleOverflow = ()=>[\n            'auto',\n            'hidden',\n            'clip',\n            'visible',\n            'scroll'\n        ];\n    const scaleOverscroll = ()=>[\n            'auto',\n            'contain',\n            'none'\n        ];\n    const scaleUnambiguousSpacing = ()=>[\n            isArbitraryVariable,\n            isArbitraryValue,\n            themeSpacing\n        ];\n    const scaleInset = ()=>[\n            isFraction,\n            'full',\n            'auto',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleGridTemplateColsRows = ()=>[\n            isInteger,\n            'none',\n            'subgrid',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartAndEnd = ()=>[\n            'auto',\n            {\n                span: [\n                    'full',\n                    isInteger,\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            },\n            isInteger,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridColRowStartOrEnd = ()=>[\n            isInteger,\n            'auto',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleGridAutoColsRows = ()=>[\n            'auto',\n            'min',\n            'max',\n            'fr',\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleAlignPrimaryAxis = ()=>[\n            'start',\n            'end',\n            'center',\n            'between',\n            'around',\n            'evenly',\n            'stretch',\n            'baseline',\n            'center-safe',\n            'end-safe'\n        ];\n    const scaleAlignSecondaryAxis = ()=>[\n            'start',\n            'end',\n            'center',\n            'stretch',\n            'center-safe',\n            'end-safe'\n        ];\n    const scaleMargin = ()=>[\n            'auto',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleSizing = ()=>[\n            isFraction,\n            'auto',\n            'full',\n            'dvw',\n            'dvh',\n            'lvw',\n            'lvh',\n            'svw',\n            'svh',\n            'min',\n            'max',\n            'fit',\n            ...scaleUnambiguousSpacing()\n        ];\n    const scaleColor = ()=>[\n            themeColor,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBgPosition = ()=>[\n            ...scalePosition(),\n            isArbitraryVariablePosition,\n            isArbitraryPosition,\n            {\n                position: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleBgRepeat = ()=>[\n            'no-repeat',\n            {\n                repeat: [\n                    '',\n                    'x',\n                    'y',\n                    'space',\n                    'round'\n                ]\n            }\n        ];\n    const scaleBgSize = ()=>[\n            'auto',\n            'cover',\n            'contain',\n            isArbitraryVariableSize,\n            isArbitrarySize,\n            {\n                size: [\n                    isArbitraryVariable,\n                    isArbitraryValue\n                ]\n            }\n        ];\n    const scaleGradientStopPosition = ()=>[\n            isPercent,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleRadius = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            'full',\n            themeRadius,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleBorderWidth = ()=>[\n            '',\n            isNumber,\n            isArbitraryVariableLength,\n            isArbitraryLength\n        ];\n    const scaleLineStyle = ()=>[\n            'solid',\n            'dashed',\n            'dotted',\n            'double'\n        ];\n    const scaleBlendMode = ()=>[\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity'\n        ];\n    const scaleMaskImagePosition = ()=>[\n            isNumber,\n            isPercent,\n            isArbitraryVariablePosition,\n            isArbitraryPosition\n        ];\n    const scaleBlur = ()=>[\n            // Deprecated since Tailwind CSS v4.0.0\n            '',\n            'none',\n            themeBlur,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleRotate = ()=>[\n            'none',\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleScale = ()=>[\n            'none',\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleSkew = ()=>[\n            isNumber,\n            isArbitraryVariable,\n            isArbitraryValue\n        ];\n    const scaleTranslate = ()=>[\n            isFraction,\n            'full',\n            ...scaleUnambiguousSpacing()\n        ];\n    return {\n        cacheSize: 500,\n        theme: {\n            animate: [\n                'spin',\n                'ping',\n                'pulse',\n                'bounce'\n            ],\n            aspect: [\n                'video'\n            ],\n            blur: [\n                isTshirtSize\n            ],\n            breakpoint: [\n                isTshirtSize\n            ],\n            color: [\n                isAny\n            ],\n            container: [\n                isTshirtSize\n            ],\n            'drop-shadow': [\n                isTshirtSize\n            ],\n            ease: [\n                'in',\n                'out',\n                'in-out'\n            ],\n            font: [\n                isAnyNonArbitrary\n            ],\n            'font-weight': [\n                'thin',\n                'extralight',\n                'light',\n                'normal',\n                'medium',\n                'semibold',\n                'bold',\n                'extrabold',\n                'black'\n            ],\n            'inset-shadow': [\n                isTshirtSize\n            ],\n            leading: [\n                'none',\n                'tight',\n                'snug',\n                'normal',\n                'relaxed',\n                'loose'\n            ],\n            perspective: [\n                'dramatic',\n                'near',\n                'normal',\n                'midrange',\n                'distant',\n                'none'\n            ],\n            radius: [\n                isTshirtSize\n            ],\n            shadow: [\n                isTshirtSize\n            ],\n            spacing: [\n                'px',\n                isNumber\n            ],\n            text: [\n                isTshirtSize\n            ],\n            'text-shadow': [\n                isTshirtSize\n            ],\n            tracking: [\n                'tighter',\n                'tight',\n                'normal',\n                'wide',\n                'wider',\n                'widest'\n            ]\n        },\n        classGroups: {\n            // --------------\n            // --- Layout ---\n            // --------------\n            /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */ aspect: [\n                {\n                    aspect: [\n                        'auto',\n                        'square',\n                        isFraction,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeAspect\n                    ]\n                }\n            ],\n            /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */ container: [\n                'container'\n            ],\n            /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */ columns: [\n                {\n                    columns: [\n                        isNumber,\n                        isArbitraryValue,\n                        isArbitraryVariable,\n                        themeContainer\n                    ]\n                }\n            ],\n            /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */ 'break-after': [\n                {\n                    'break-after': scaleBreak()\n                }\n            ],\n            /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */ 'break-before': [\n                {\n                    'break-before': scaleBreak()\n                }\n            ],\n            /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */ 'break-inside': [\n                {\n                    'break-inside': [\n                        'auto',\n                        'avoid',\n                        'avoid-page',\n                        'avoid-column'\n                    ]\n                }\n            ],\n            /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */ 'box-decoration': [\n                {\n                    'box-decoration': [\n                        'slice',\n                        'clone'\n                    ]\n                }\n            ],\n            /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */ box: [\n                {\n                    box: [\n                        'border',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */ display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden'\n            ],\n            /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */ sr: [\n                'sr-only',\n                'not-sr-only'\n            ],\n            /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */ float: [\n                {\n                    float: [\n                        'right',\n                        'left',\n                        'none',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */ clear: [\n                {\n                    clear: [\n                        'left',\n                        'right',\n                        'both',\n                        'none',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */ isolation: [\n                'isolate',\n                'isolation-auto'\n            ],\n            /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */ 'object-fit': [\n                {\n                    object: [\n                        'contain',\n                        'cover',\n                        'fill',\n                        'none',\n                        'scale-down'\n                    ]\n                }\n            ],\n            /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */ 'object-position': [\n                {\n                    object: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */ overflow: [\n                {\n                    overflow: scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */ 'overflow-x': [\n                {\n                    'overflow-x': scaleOverflow()\n                }\n            ],\n            /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */ 'overflow-y': [\n                {\n                    'overflow-y': scaleOverflow()\n                }\n            ],\n            /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ overscroll: [\n                {\n                    overscroll: scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ 'overscroll-x': [\n                {\n                    'overscroll-x': scaleOverscroll()\n                }\n            ],\n            /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */ 'overscroll-y': [\n                {\n                    'overscroll-y': scaleOverscroll()\n                }\n            ],\n            /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */ position: [\n                'static',\n                'fixed',\n                'absolute',\n                'relative',\n                'sticky'\n            ],\n            /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ inset: [\n                {\n                    inset: scaleInset()\n                }\n            ],\n            /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ 'inset-x': [\n                {\n                    'inset-x': scaleInset()\n                }\n            ],\n            /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ 'inset-y': [\n                {\n                    'inset-y': scaleInset()\n                }\n            ],\n            /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ start: [\n                {\n                    start: scaleInset()\n                }\n            ],\n            /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ end: [\n                {\n                    end: scaleInset()\n                }\n            ],\n            /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ top: [\n                {\n                    top: scaleInset()\n                }\n            ],\n            /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ right: [\n                {\n                    right: scaleInset()\n                }\n            ],\n            /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ bottom: [\n                {\n                    bottom: scaleInset()\n                }\n            ],\n            /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */ left: [\n                {\n                    left: scaleInset()\n                }\n            ],\n            /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */ visibility: [\n                'visible',\n                'invisible',\n                'collapse'\n            ],\n            /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */ z: [\n                {\n                    z: [\n                        isInteger,\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------------\n            // --- Flexbox and Grid ---\n            // ------------------------\n            /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */ basis: [\n                {\n                    basis: [\n                        isFraction,\n                        'full',\n                        'auto',\n                        themeContainer,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */ 'flex-direction': [\n                {\n                    flex: [\n                        'row',\n                        'row-reverse',\n                        'col',\n                        'col-reverse'\n                    ]\n                }\n            ],\n            /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */ 'flex-wrap': [\n                {\n                    flex: [\n                        'nowrap',\n                        'wrap',\n                        'wrap-reverse'\n                    ]\n                }\n            ],\n            /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */ flex: [\n                {\n                    flex: [\n                        isNumber,\n                        isFraction,\n                        'auto',\n                        'initial',\n                        'none',\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */ grow: [\n                {\n                    grow: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */ shrink: [\n                {\n                    shrink: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */ order: [\n                {\n                    order: [\n                        isInteger,\n                        'first',\n                        'last',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */ 'grid-cols': [\n                {\n                    'grid-cols': scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-start-end': [\n                {\n                    col: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-start': [\n                {\n                    'col-start': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */ 'col-end': [\n                {\n                    'col-end': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */ 'grid-rows': [\n                {\n                    'grid-rows': scaleGridTemplateColsRows()\n                }\n            ],\n            /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-start-end': [\n                {\n                    row: scaleGridColRowStartAndEnd()\n                }\n            ],\n            /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-start': [\n                {\n                    'row-start': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */ 'row-end': [\n                {\n                    'row-end': scaleGridColRowStartOrEnd()\n                }\n            ],\n            /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */ 'grid-flow': [\n                {\n                    'grid-flow': [\n                        'row',\n                        'col',\n                        'dense',\n                        'row-dense',\n                        'col-dense'\n                    ]\n                }\n            ],\n            /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */ 'auto-cols': [\n                {\n                    'auto-cols': scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */ 'auto-rows': [\n                {\n                    'auto-rows': scaleGridAutoColsRows()\n                }\n            ],\n            /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */ gap: [\n                {\n                    gap: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */ 'gap-x': [\n                {\n                    'gap-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */ 'gap-y': [\n                {\n                    'gap-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */ 'justify-content': [\n                {\n                    justify: [\n                        ...scaleAlignPrimaryAxis(),\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */ 'justify-items': [\n                {\n                    'justify-items': [\n                        ...scaleAlignSecondaryAxis(),\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */ 'justify-self': [\n                {\n                    'justify-self': [\n                        'auto',\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */ 'align-content': [\n                {\n                    content: [\n                        'normal',\n                        ...scaleAlignPrimaryAxis()\n                    ]\n                }\n            ],\n            /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */ 'align-items': [\n                {\n                    items: [\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                '',\n                                'last'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */ 'align-self': [\n                {\n                    self: [\n                        'auto',\n                        ...scaleAlignSecondaryAxis(),\n                        {\n                            baseline: [\n                                '',\n                                'last'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */ 'place-content': [\n                {\n                    'place-content': scaleAlignPrimaryAxis()\n                }\n            ],\n            /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */ 'place-items': [\n                {\n                    'place-items': [\n                        ...scaleAlignSecondaryAxis(),\n                        'baseline'\n                    ]\n                }\n            ],\n            /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */ 'place-self': [\n                {\n                    'place-self': [\n                        'auto',\n                        ...scaleAlignSecondaryAxis()\n                    ]\n                }\n            ],\n            // Spacing\n            /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */ p: [\n                {\n                    p: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */ px: [\n                {\n                    px: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */ py: [\n                {\n                    py: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */ ps: [\n                {\n                    ps: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */ pe: [\n                {\n                    pe: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */ pt: [\n                {\n                    pt: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */ pr: [\n                {\n                    pr: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */ pb: [\n                {\n                    pb: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */ pl: [\n                {\n                    pl: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */ m: [\n                {\n                    m: scaleMargin()\n                }\n            ],\n            /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */ mx: [\n                {\n                    mx: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */ my: [\n                {\n                    my: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */ ms: [\n                {\n                    ms: scaleMargin()\n                }\n            ],\n            /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */ me: [\n                {\n                    me: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */ mt: [\n                {\n                    mt: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */ mr: [\n                {\n                    mr: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */ mb: [\n                {\n                    mb: scaleMargin()\n                }\n            ],\n            /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */ ml: [\n                {\n                    ml: scaleMargin()\n                }\n            ],\n            /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-x': [\n                {\n                    'space-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-x-reverse': [\n                'space-x-reverse'\n            ],\n            /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-y': [\n                {\n                    'space-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */ 'space-y-reverse': [\n                'space-y-reverse'\n            ],\n            // --------------\n            // --- Sizing ---\n            // --------------\n            /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */ size: [\n                {\n                    size: scaleSizing()\n                }\n            ],\n            /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */ w: [\n                {\n                    w: [\n                        themeContainer,\n                        'screen',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */ 'min-w': [\n                {\n                    'min-w': [\n                        themeContainer,\n                        'screen',\n                        /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'none',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */ 'max-w': [\n                {\n                    'max-w': [\n                        themeContainer,\n                        'screen',\n                        'none',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ 'prose',\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ {\n                            screen: [\n                                themeBreakpoint\n                            ]\n                        },\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */ h: [\n                {\n                    h: [\n                        'screen',\n                        'lh',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */ 'min-h': [\n                {\n                    'min-h': [\n                        'screen',\n                        'lh',\n                        'none',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */ 'max-h': [\n                {\n                    'max-h': [\n                        'screen',\n                        'lh',\n                        ...scaleSizing()\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Typography ---\n            // ------------------\n            /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */ 'font-size': [\n                {\n                    text: [\n                        'base',\n                        themeText,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */ 'font-smoothing': [\n                'antialiased',\n                'subpixel-antialiased'\n            ],\n            /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */ 'font-style': [\n                'italic',\n                'not-italic'\n            ],\n            /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */ 'font-weight': [\n                {\n                    font: [\n                        themeFontWeight,\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */ 'font-stretch': [\n                {\n                    'font-stretch': [\n                        'ultra-condensed',\n                        'extra-condensed',\n                        'condensed',\n                        'semi-condensed',\n                        'normal',\n                        'semi-expanded',\n                        'expanded',\n                        'extra-expanded',\n                        'ultra-expanded',\n                        isPercent,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */ 'font-family': [\n                {\n                    font: [\n                        isArbitraryVariableFamilyName,\n                        isArbitraryValue,\n                        themeFont\n                    ]\n                }\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-normal': [\n                'normal-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-ordinal': [\n                'ordinal'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-slashed-zero': [\n                'slashed-zero'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-figure': [\n                'lining-nums',\n                'oldstyle-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-spacing': [\n                'proportional-nums',\n                'tabular-nums'\n            ],\n            /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */ 'fvn-fraction': [\n                'diagonal-fractions',\n                'stacked-fractions'\n            ],\n            /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */ tracking: [\n                {\n                    tracking: [\n                        themeTracking,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */ 'line-clamp': [\n                {\n                    'line-clamp': [\n                        isNumber,\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */ leading: [\n                {\n                    leading: [\n                        /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */ themeLeading,\n                        ...scaleUnambiguousSpacing()\n                    ]\n                }\n            ],\n            /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */ 'list-image': [\n                {\n                    'list-image': [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */ 'list-style-position': [\n                {\n                    list: [\n                        'inside',\n                        'outside'\n                    ]\n                }\n            ],\n            /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */ 'list-style-type': [\n                {\n                    list: [\n                        'disc',\n                        'decimal',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */ 'text-alignment': [\n                {\n                    text: [\n                        'left',\n                        'center',\n                        'right',\n                        'justify',\n                        'start',\n                        'end'\n                    ]\n                }\n            ],\n            /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */ 'placeholder-color': [\n                {\n                    placeholder: scaleColor()\n                }\n            ],\n            /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */ 'text-color': [\n                {\n                    text: scaleColor()\n                }\n            ],\n            /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */ 'text-decoration': [\n                'underline',\n                'overline',\n                'line-through',\n                'no-underline'\n            ],\n            /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */ 'text-decoration-style': [\n                {\n                    decoration: [\n                        ...scaleLineStyle(),\n                        'wavy'\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */ 'text-decoration-thickness': [\n                {\n                    decoration: [\n                        isNumber,\n                        'from-font',\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */ 'text-decoration-color': [\n                {\n                    decoration: scaleColor()\n                }\n            ],\n            /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */ 'underline-offset': [\n                {\n                    'underline-offset': [\n                        isNumber,\n                        'auto',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */ 'text-transform': [\n                'uppercase',\n                'lowercase',\n                'capitalize',\n                'normal-case'\n            ],\n            /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */ 'text-overflow': [\n                'truncate',\n                'text-ellipsis',\n                'text-clip'\n            ],\n            /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */ 'text-wrap': [\n                {\n                    text: [\n                        'wrap',\n                        'nowrap',\n                        'balance',\n                        'pretty'\n                    ]\n                }\n            ],\n            /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */ indent: [\n                {\n                    indent: scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */ 'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */ whitespace: [\n                {\n                    whitespace: [\n                        'normal',\n                        'nowrap',\n                        'pre',\n                        'pre-line',\n                        'pre-wrap',\n                        'break-spaces'\n                    ]\n                }\n            ],\n            /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */ break: [\n                {\n                    break: [\n                        'normal',\n                        'words',\n                        'all',\n                        'keep'\n                    ]\n                }\n            ],\n            /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */ wrap: [\n                {\n                    wrap: [\n                        'break-word',\n                        'anywhere',\n                        'normal'\n                    ]\n                }\n            ],\n            /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */ hyphens: [\n                {\n                    hyphens: [\n                        'none',\n                        'manual',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */ content: [\n                {\n                    content: [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -------------------\n            // --- Backgrounds ---\n            // -------------------\n            /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */ 'bg-attachment': [\n                {\n                    bg: [\n                        'fixed',\n                        'local',\n                        'scroll'\n                    ]\n                }\n            ],\n            /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */ 'bg-clip': [\n                {\n                    'bg-clip': [\n                        'border',\n                        'padding',\n                        'content',\n                        'text'\n                    ]\n                }\n            ],\n            /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */ 'bg-origin': [\n                {\n                    'bg-origin': [\n                        'border',\n                        'padding',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */ 'bg-position': [\n                {\n                    bg: scaleBgPosition()\n                }\n            ],\n            /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */ 'bg-repeat': [\n                {\n                    bg: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */ 'bg-size': [\n                {\n                    bg: scaleBgSize()\n                }\n            ],\n            /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */ 'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        {\n                            linear: [\n                                {\n                                    to: [\n                                        't',\n                                        'tr',\n                                        'r',\n                                        'br',\n                                        'b',\n                                        'bl',\n                                        'l',\n                                        'tl'\n                                    ]\n                                },\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            radial: [\n                                '',\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ],\n                            conic: [\n                                isInteger,\n                                isArbitraryVariable,\n                                isArbitraryValue\n                            ]\n                        },\n                        isArbitraryVariableImage,\n                        isArbitraryImage\n                    ]\n                }\n            ],\n            /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */ 'bg-color': [\n                {\n                    bg: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-from-pos': [\n                {\n                    from: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-via-pos': [\n                {\n                    via: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-to-pos': [\n                {\n                    to: scaleGradientStopPosition()\n                }\n            ],\n            /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-from': [\n                {\n                    from: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-via': [\n                {\n                    via: scaleColor()\n                }\n            ],\n            /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */ 'gradient-to': [\n                {\n                    to: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Borders ---\n            // ---------------\n            /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */ rounded: [\n                {\n                    rounded: scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-s': [\n                {\n                    'rounded-s': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-e': [\n                {\n                    'rounded-e': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-t': [\n                {\n                    'rounded-t': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-r': [\n                {\n                    'rounded-r': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-b': [\n                {\n                    'rounded-b': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-l': [\n                {\n                    'rounded-l': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-ss': [\n                {\n                    'rounded-ss': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-se': [\n                {\n                    'rounded-se': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-ee': [\n                {\n                    'rounded-ee': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-es': [\n                {\n                    'rounded-es': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-tl': [\n                {\n                    'rounded-tl': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-tr': [\n                {\n                    'rounded-tr': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-br': [\n                {\n                    'rounded-br': scaleRadius()\n                }\n            ],\n            /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */ 'rounded-bl': [\n                {\n                    'rounded-bl': scaleRadius()\n                }\n            ],\n            /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w': [\n                {\n                    border: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-x': [\n                {\n                    'border-x': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-y': [\n                {\n                    'border-y': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-s': [\n                {\n                    'border-s': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-e': [\n                {\n                    'border-e': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-t': [\n                {\n                    'border-t': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-r': [\n                {\n                    'border-r': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-b': [\n                {\n                    'border-b': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */ 'border-w-l': [\n                {\n                    'border-l': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-x': [\n                {\n                    'divide-x': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-x-reverse': [\n                'divide-x-reverse'\n            ],\n            /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-y': [\n                {\n                    'divide-y': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */ 'divide-y-reverse': [\n                'divide-y-reverse'\n            ],\n            /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */ 'border-style': [\n                {\n                    border: [\n                        ...scaleLineStyle(),\n                        'hidden',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */ 'divide-style': [\n                {\n                    divide: [\n                        ...scaleLineStyle(),\n                        'hidden',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color': [\n                {\n                    border: scaleColor()\n                }\n            ],\n            /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-x': [\n                {\n                    'border-x': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-y': [\n                {\n                    'border-y': scaleColor()\n                }\n            ],\n            /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-s': [\n                {\n                    'border-s': scaleColor()\n                }\n            ],\n            /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-e': [\n                {\n                    'border-e': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-t': [\n                {\n                    'border-t': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-r': [\n                {\n                    'border-r': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-b': [\n                {\n                    'border-b': scaleColor()\n                }\n            ],\n            /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */ 'border-color-l': [\n                {\n                    'border-l': scaleColor()\n                }\n            ],\n            /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */ 'divide-color': [\n                {\n                    divide: scaleColor()\n                }\n            ],\n            /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */ 'outline-style': [\n                {\n                    outline: [\n                        ...scaleLineStyle(),\n                        'none',\n                        'hidden'\n                    ]\n                }\n            ],\n            /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */ 'outline-offset': [\n                {\n                    'outline-offset': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */ 'outline-w': [\n                {\n                    outline: [\n                        '',\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */ 'outline-color': [\n                {\n                    outline: scaleColor()\n                }\n            ],\n            // ---------------\n            // --- Effects ---\n            // ---------------\n            /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */ shadow: [\n                {\n                    shadow: [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */ 'shadow-color': [\n                {\n                    shadow: scaleColor()\n                }\n            ],\n            /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */ 'inset-shadow': [\n                {\n                    'inset-shadow': [\n                        'none',\n                        themeInsetShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */ 'inset-shadow-color': [\n                {\n                    'inset-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */ 'ring-w': [\n                {\n                    ring: scaleBorderWidth()\n                }\n            ],\n            /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-w-inset': [\n                'ring-inset'\n            ],\n            /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */ 'ring-color': [\n                {\n                    ring: scaleColor()\n                }\n            ],\n            /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-offset-w': [\n                {\n                    'ring-offset': [\n                        isNumber,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */ 'ring-offset-color': [\n                {\n                    'ring-offset': scaleColor()\n                }\n            ],\n            /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */ 'inset-ring-w': [\n                {\n                    'inset-ring': scaleBorderWidth()\n                }\n            ],\n            /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */ 'inset-ring-color': [\n                {\n                    'inset-ring': scaleColor()\n                }\n            ],\n            /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */ 'text-shadow': [\n                {\n                    'text-shadow': [\n                        'none',\n                        themeTextShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */ 'text-shadow-color': [\n                {\n                    'text-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */ opacity: [\n                {\n                    opacity: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */ 'mix-blend': [\n                {\n                    'mix-blend': [\n                        ...scaleBlendMode(),\n                        'plus-darker',\n                        'plus-lighter'\n                    ]\n                }\n            ],\n            /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */ 'bg-blend': [\n                {\n                    'bg-blend': scaleBlendMode()\n                }\n            ],\n            /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */ 'mask-clip': [\n                {\n                    'mask-clip': [\n                        'border',\n                        'padding',\n                        'content',\n                        'fill',\n                        'stroke',\n                        'view'\n                    ]\n                },\n                'mask-no-clip'\n            ],\n            /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */ 'mask-composite': [\n                {\n                    mask: [\n                        'add',\n                        'subtract',\n                        'intersect',\n                        'exclude'\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ 'mask-image-linear-pos': [\n                {\n                    'mask-linear': [\n                        isNumber\n                    ]\n                }\n            ],\n            'mask-image-linear-from-pos': [\n                {\n                    'mask-linear-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-linear-to-pos': [\n                {\n                    'mask-linear-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-linear-from-color': [\n                {\n                    'mask-linear-from': scaleColor()\n                }\n            ],\n            'mask-image-linear-to-color': [\n                {\n                    'mask-linear-to': scaleColor()\n                }\n            ],\n            'mask-image-t-from-pos': [\n                {\n                    'mask-t-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-t-to-pos': [\n                {\n                    'mask-t-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-t-from-color': [\n                {\n                    'mask-t-from': scaleColor()\n                }\n            ],\n            'mask-image-t-to-color': [\n                {\n                    'mask-t-to': scaleColor()\n                }\n            ],\n            'mask-image-r-from-pos': [\n                {\n                    'mask-r-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-r-to-pos': [\n                {\n                    'mask-r-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-r-from-color': [\n                {\n                    'mask-r-from': scaleColor()\n                }\n            ],\n            'mask-image-r-to-color': [\n                {\n                    'mask-r-to': scaleColor()\n                }\n            ],\n            'mask-image-b-from-pos': [\n                {\n                    'mask-b-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-b-to-pos': [\n                {\n                    'mask-b-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-b-from-color': [\n                {\n                    'mask-b-from': scaleColor()\n                }\n            ],\n            'mask-image-b-to-color': [\n                {\n                    'mask-b-to': scaleColor()\n                }\n            ],\n            'mask-image-l-from-pos': [\n                {\n                    'mask-l-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-l-to-pos': [\n                {\n                    'mask-l-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-l-from-color': [\n                {\n                    'mask-l-from': scaleColor()\n                }\n            ],\n            'mask-image-l-to-color': [\n                {\n                    'mask-l-to': scaleColor()\n                }\n            ],\n            'mask-image-x-from-pos': [\n                {\n                    'mask-x-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-x-to-pos': [\n                {\n                    'mask-x-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-x-from-color': [\n                {\n                    'mask-x-from': scaleColor()\n                }\n            ],\n            'mask-image-x-to-color': [\n                {\n                    'mask-x-to': scaleColor()\n                }\n            ],\n            'mask-image-y-from-pos': [\n                {\n                    'mask-y-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-y-to-pos': [\n                {\n                    'mask-y-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-y-from-color': [\n                {\n                    'mask-y-from': scaleColor()\n                }\n            ],\n            'mask-image-y-to-color': [\n                {\n                    'mask-y-to': scaleColor()\n                }\n            ],\n            'mask-image-radial': [\n                {\n                    'mask-radial': [\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            'mask-image-radial-from-pos': [\n                {\n                    'mask-radial-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-radial-to-pos': [\n                {\n                    'mask-radial-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-radial-from-color': [\n                {\n                    'mask-radial-from': scaleColor()\n                }\n            ],\n            'mask-image-radial-to-color': [\n                {\n                    'mask-radial-to': scaleColor()\n                }\n            ],\n            'mask-image-radial-shape': [\n                {\n                    'mask-radial': [\n                        'circle',\n                        'ellipse'\n                    ]\n                }\n            ],\n            'mask-image-radial-size': [\n                {\n                    'mask-radial': [\n                        {\n                            closest: [\n                                'side',\n                                'corner'\n                            ],\n                            farthest: [\n                                'side',\n                                'corner'\n                            ]\n                        }\n                    ]\n                }\n            ],\n            'mask-image-radial-pos': [\n                {\n                    'mask-radial-at': scalePosition()\n                }\n            ],\n            'mask-image-conic-pos': [\n                {\n                    'mask-conic': [\n                        isNumber\n                    ]\n                }\n            ],\n            'mask-image-conic-from-pos': [\n                {\n                    'mask-conic-from': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-conic-to-pos': [\n                {\n                    'mask-conic-to': scaleMaskImagePosition()\n                }\n            ],\n            'mask-image-conic-from-color': [\n                {\n                    'mask-conic-from': scaleColor()\n                }\n            ],\n            'mask-image-conic-to-color': [\n                {\n                    'mask-conic-to': scaleColor()\n                }\n            ],\n            /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */ 'mask-mode': [\n                {\n                    mask: [\n                        'alpha',\n                        'luminance',\n                        'match'\n                    ]\n                }\n            ],\n            /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */ 'mask-origin': [\n                {\n                    'mask-origin': [\n                        'border',\n                        'padding',\n                        'content',\n                        'fill',\n                        'stroke',\n                        'view'\n                    ]\n                }\n            ],\n            /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */ 'mask-position': [\n                {\n                    mask: scaleBgPosition()\n                }\n            ],\n            /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */ 'mask-repeat': [\n                {\n                    mask: scaleBgRepeat()\n                }\n            ],\n            /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */ 'mask-size': [\n                {\n                    mask: scaleBgSize()\n                }\n            ],\n            /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */ 'mask-type': [\n                {\n                    'mask-type': [\n                        'alpha',\n                        'luminance'\n                    ]\n                }\n            ],\n            /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */ 'mask-image': [\n                {\n                    mask: [\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ---------------\n            // --- Filters ---\n            // ---------------\n            /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */ filter: [\n                {\n                    filter: [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */ blur: [\n                {\n                    blur: scaleBlur()\n                }\n            ],\n            /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */ brightness: [\n                {\n                    brightness: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */ contrast: [\n                {\n                    contrast: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */ 'drop-shadow': [\n                {\n                    'drop-shadow': [\n                        // Deprecated since Tailwind CSS v4.0.0\n                        '',\n                        'none',\n                        themeDropShadow,\n                        isArbitraryVariableShadow,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */ 'drop-shadow-color': [\n                {\n                    'drop-shadow': scaleColor()\n                }\n            ],\n            /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */ grayscale: [\n                {\n                    grayscale: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */ 'hue-rotate': [\n                {\n                    'hue-rotate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */ invert: [\n                {\n                    invert: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */ saturate: [\n                {\n                    saturate: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */ sepia: [\n                {\n                    sepia: [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */ 'backdrop-filter': [\n                {\n                    'backdrop-filter': [\n                        // Deprecated since Tailwind CSS v3.0.0\n                        '',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */ 'backdrop-blur': [\n                {\n                    'backdrop-blur': scaleBlur()\n                }\n            ],\n            /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */ 'backdrop-brightness': [\n                {\n                    'backdrop-brightness': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */ 'backdrop-contrast': [\n                {\n                    'backdrop-contrast': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */ 'backdrop-grayscale': [\n                {\n                    'backdrop-grayscale': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */ 'backdrop-hue-rotate': [\n                {\n                    'backdrop-hue-rotate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */ 'backdrop-invert': [\n                {\n                    'backdrop-invert': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */ 'backdrop-opacity': [\n                {\n                    'backdrop-opacity': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */ 'backdrop-saturate': [\n                {\n                    'backdrop-saturate': [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */ 'backdrop-sepia': [\n                {\n                    'backdrop-sepia': [\n                        '',\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // --------------\n            // --- Tables ---\n            // --------------\n            /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */ 'border-collapse': [\n                {\n                    border: [\n                        'collapse',\n                        'separate'\n                    ]\n                }\n            ],\n            /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing': [\n                {\n                    'border-spacing': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing-x': [\n                {\n                    'border-spacing-x': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */ 'border-spacing-y': [\n                {\n                    'border-spacing-y': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */ 'table-layout': [\n                {\n                    table: [\n                        'auto',\n                        'fixed'\n                    ]\n                }\n            ],\n            /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */ caption: [\n                {\n                    caption: [\n                        'top',\n                        'bottom'\n                    ]\n                }\n            ],\n            // ---------------------------------\n            // --- Transitions and Animation ---\n            // ---------------------------------\n            /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */ transition: [\n                {\n                    transition: [\n                        '',\n                        'all',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        'none',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */ 'transition-behavior': [\n                {\n                    transition: [\n                        'normal',\n                        'discrete'\n                    ]\n                }\n            ],\n            /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */ duration: [\n                {\n                    duration: [\n                        isNumber,\n                        'initial',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */ ease: [\n                {\n                    ease: [\n                        'linear',\n                        'initial',\n                        themeEase,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */ delay: [\n                {\n                    delay: [\n                        isNumber,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */ animate: [\n                {\n                    animate: [\n                        'none',\n                        themeAnimate,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // ------------------\n            // --- Transforms ---\n            // ------------------\n            /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */ backface: [\n                {\n                    backface: [\n                        'hidden',\n                        'visible'\n                    ]\n                }\n            ],\n            /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */ perspective: [\n                {\n                    perspective: [\n                        themePerspective,\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */ 'perspective-origin': [\n                {\n                    'perspective-origin': scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */ rotate: [\n                {\n                    rotate: scaleRotate()\n                }\n            ],\n            /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-x': [\n                {\n                    'rotate-x': scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-y': [\n                {\n                    'rotate-y': scaleRotate()\n                }\n            ],\n            /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */ 'rotate-z': [\n                {\n                    'rotate-z': scaleRotate()\n                }\n            ],\n            /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */ scale: [\n                {\n                    scale: scaleScale()\n                }\n            ],\n            /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-x': [\n                {\n                    'scale-x': scaleScale()\n                }\n            ],\n            /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-y': [\n                {\n                    'scale-y': scaleScale()\n                }\n            ],\n            /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-z': [\n                {\n                    'scale-z': scaleScale()\n                }\n            ],\n            /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */ 'scale-3d': [\n                'scale-3d'\n            ],\n            /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */ skew: [\n                {\n                    skew: scaleSkew()\n                }\n            ],\n            /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */ 'skew-x': [\n                {\n                    'skew-x': scaleSkew()\n                }\n            ],\n            /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */ 'skew-y': [\n                {\n                    'skew-y': scaleSkew()\n                }\n            ],\n            /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */ transform: [\n                {\n                    transform: [\n                        isArbitraryVariable,\n                        isArbitraryValue,\n                        '',\n                        'none',\n                        'gpu',\n                        'cpu'\n                    ]\n                }\n            ],\n            /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */ 'transform-origin': [\n                {\n                    origin: scalePositionWithArbitrary()\n                }\n            ],\n            /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */ 'transform-style': [\n                {\n                    transform: [\n                        '3d',\n                        'flat'\n                    ]\n                }\n            ],\n            /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */ translate: [\n                {\n                    translate: scaleTranslate()\n                }\n            ],\n            /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-x': [\n                {\n                    'translate-x': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-y': [\n                {\n                    'translate-y': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-z': [\n                {\n                    'translate-z': scaleTranslate()\n                }\n            ],\n            /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */ 'translate-none': [\n                'translate-none'\n            ],\n            // ---------------------\n            // --- Interactivity ---\n            // ---------------------\n            /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */ accent: [\n                {\n                    accent: scaleColor()\n                }\n            ],\n            /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */ appearance: [\n                {\n                    appearance: [\n                        'none',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */ 'caret-color': [\n                {\n                    caret: scaleColor()\n                }\n            ],\n            /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */ 'color-scheme': [\n                {\n                    scheme: [\n                        'normal',\n                        'dark',\n                        'light',\n                        'light-dark',\n                        'only-dark',\n                        'only-light'\n                    ]\n                }\n            ],\n            /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */ cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */ 'field-sizing': [\n                {\n                    'field-sizing': [\n                        'fixed',\n                        'content'\n                    ]\n                }\n            ],\n            /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */ 'pointer-events': [\n                {\n                    'pointer-events': [\n                        'auto',\n                        'none'\n                    ]\n                }\n            ],\n            /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */ resize: [\n                {\n                    resize: [\n                        'none',\n                        '',\n                        'y',\n                        'x'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */ 'scroll-behavior': [\n                {\n                    scroll: [\n                        'auto',\n                        'smooth'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-m': [\n                {\n                    'scroll-m': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mx': [\n                {\n                    'scroll-mx': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-my': [\n                {\n                    'scroll-my': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-ms': [\n                {\n                    'scroll-ms': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-me': [\n                {\n                    'scroll-me': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mt': [\n                {\n                    'scroll-mt': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mr': [\n                {\n                    'scroll-mr': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-mb': [\n                {\n                    'scroll-mb': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */ 'scroll-ml': [\n                {\n                    'scroll-ml': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-p': [\n                {\n                    'scroll-p': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-px': [\n                {\n                    'scroll-px': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-py': [\n                {\n                    'scroll-py': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-ps': [\n                {\n                    'scroll-ps': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pe': [\n                {\n                    'scroll-pe': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pt': [\n                {\n                    'scroll-pt': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pr': [\n                {\n                    'scroll-pr': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pb': [\n                {\n                    'scroll-pb': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */ 'scroll-pl': [\n                {\n                    'scroll-pl': scaleUnambiguousSpacing()\n                }\n            ],\n            /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */ 'snap-align': [\n                {\n                    snap: [\n                        'start',\n                        'end',\n                        'center',\n                        'align-none'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */ 'snap-stop': [\n                {\n                    snap: [\n                        'normal',\n                        'always'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ 'snap-type': [\n                {\n                    snap: [\n                        'none',\n                        'x',\n                        'y',\n                        'both'\n                    ]\n                }\n            ],\n            /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */ 'snap-strictness': [\n                {\n                    snap: [\n                        'mandatory',\n                        'proximity'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */ touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'manipulation'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-x': [\n                {\n                    'touch-pan': [\n                        'x',\n                        'left',\n                        'right'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-y': [\n                {\n                    'touch-pan': [\n                        'y',\n                        'up',\n                        'down'\n                    ]\n                }\n            ],\n            /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */ 'touch-pz': [\n                'touch-pinch-zoom'\n            ],\n            /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */ select: [\n                {\n                    select: [\n                        'none',\n                        'text',\n                        'all',\n                        'auto'\n                    ]\n                }\n            ],\n            /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */ 'will-change': [\n                {\n                    'will-change': [\n                        'auto',\n                        'scroll',\n                        'contents',\n                        'transform',\n                        isArbitraryVariable,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // -----------\n            // --- SVG ---\n            // -----------\n            /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */ fill: [\n                {\n                    fill: [\n                        'none',\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */ 'stroke-w': [\n                {\n                    stroke: [\n                        isNumber,\n                        isArbitraryVariableLength,\n                        isArbitraryLength,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */ stroke: [\n                {\n                    stroke: [\n                        'none',\n                        ...scaleColor()\n                    ]\n                }\n            ],\n            // ---------------------\n            // --- Accessibility ---\n            // ---------------------\n            /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */ 'forced-color-adjust': [\n                {\n                    'forced-color-adjust': [\n                        'auto',\n                        'none'\n                    ]\n                }\n            ]\n        },\n        conflictingClassGroups: {\n            overflow: [\n                'overflow-x',\n                'overflow-y'\n            ],\n            overscroll: [\n                'overscroll-x',\n                'overscroll-y'\n            ],\n            inset: [\n                'inset-x',\n                'inset-y',\n                'start',\n                'end',\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ],\n            'inset-x': [\n                'right',\n                'left'\n            ],\n            'inset-y': [\n                'top',\n                'bottom'\n            ],\n            flex: [\n                'basis',\n                'grow',\n                'shrink'\n            ],\n            gap: [\n                'gap-x',\n                'gap-y'\n            ],\n            p: [\n                'px',\n                'py',\n                'ps',\n                'pe',\n                'pt',\n                'pr',\n                'pb',\n                'pl'\n            ],\n            px: [\n                'pr',\n                'pl'\n            ],\n            py: [\n                'pt',\n                'pb'\n            ],\n            m: [\n                'mx',\n                'my',\n                'ms',\n                'me',\n                'mt',\n                'mr',\n                'mb',\n                'ml'\n            ],\n            mx: [\n                'mr',\n                'ml'\n            ],\n            my: [\n                'mt',\n                'mb'\n            ],\n            size: [\n                'w',\n                'h'\n            ],\n            'font-size': [\n                'leading'\n            ],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction'\n            ],\n            'fvn-ordinal': [\n                'fvn-normal'\n            ],\n            'fvn-slashed-zero': [\n                'fvn-normal'\n            ],\n            'fvn-figure': [\n                'fvn-normal'\n            ],\n            'fvn-spacing': [\n                'fvn-normal'\n            ],\n            'fvn-fraction': [\n                'fvn-normal'\n            ],\n            'line-clamp': [\n                'display',\n                'overflow'\n            ],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl'\n            ],\n            'rounded-s': [\n                'rounded-ss',\n                'rounded-es'\n            ],\n            'rounded-e': [\n                'rounded-se',\n                'rounded-ee'\n            ],\n            'rounded-t': [\n                'rounded-tl',\n                'rounded-tr'\n            ],\n            'rounded-r': [\n                'rounded-tr',\n                'rounded-br'\n            ],\n            'rounded-b': [\n                'rounded-br',\n                'rounded-bl'\n            ],\n            'rounded-l': [\n                'rounded-tl',\n                'rounded-bl'\n            ],\n            'border-spacing': [\n                'border-spacing-x',\n                'border-spacing-y'\n            ],\n            'border-w': [\n                'border-w-x',\n                'border-w-y',\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l'\n            ],\n            'border-w-x': [\n                'border-w-r',\n                'border-w-l'\n            ],\n            'border-w-y': [\n                'border-w-t',\n                'border-w-b'\n            ],\n            'border-color': [\n                'border-color-x',\n                'border-color-y',\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l'\n            ],\n            'border-color-x': [\n                'border-color-r',\n                'border-color-l'\n            ],\n            'border-color-y': [\n                'border-color-t',\n                'border-color-b'\n            ],\n            translate: [\n                'translate-x',\n                'translate-y',\n                'translate-none'\n            ],\n            'translate-none': [\n                'translate',\n                'translate-x',\n                'translate-y',\n                'translate-z'\n            ],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml'\n            ],\n            'scroll-mx': [\n                'scroll-mr',\n                'scroll-ml'\n            ],\n            'scroll-my': [\n                'scroll-mt',\n                'scroll-mb'\n            ],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl'\n            ],\n            'scroll-px': [\n                'scroll-pr',\n                'scroll-pl'\n            ],\n            'scroll-py': [\n                'scroll-pt',\n                'scroll-pb'\n            ],\n            touch: [\n                'touch-x',\n                'touch-y',\n                'touch-pz'\n            ],\n            'touch-x': [\n                'touch'\n            ],\n            'touch-y': [\n                'touch'\n            ],\n            'touch-pz': [\n                'touch'\n            ]\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': [\n                'leading'\n            ]\n        },\n        orderSensitiveModifiers: [\n            '*',\n            '**',\n            'after',\n            'backdrop',\n            'before',\n            'details-content',\n            'file',\n            'first-letter',\n            'first-line',\n            'marker',\n            'placeholder',\n            'selection'\n        ]\n    };\n};\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */ const mergeConfigs = (baseConfig, param)=>{\n    let { cacheSize, prefix, experimentalParseClassName, extend = {}, override = {} } = param;\n    overrideProperty(baseConfig, 'cacheSize', cacheSize);\n    overrideProperty(baseConfig, 'prefix', prefix);\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n    overrideConfigProperties(baseConfig.theme, override.theme);\n    overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n    overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n    overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n    mergeConfigProperties(baseConfig.theme, extend.theme);\n    mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n    mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n    mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n    return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue)=>{\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue;\n    }\n};\nconst overrideConfigProperties = (baseObject, overrideObject)=>{\n    if (overrideObject) {\n        for(const key in overrideObject){\n            overrideProperty(baseObject, key, overrideObject[key]);\n        }\n    }\n};\nconst mergeConfigProperties = (baseObject, mergeObject)=>{\n    if (mergeObject) {\n        for(const key in mergeObject){\n            mergeArrayProperties(baseObject, mergeObject, key);\n        }\n    }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key)=>{\n    const mergeValue = mergeObject[key];\n    if (mergeValue !== undefined) {\n        baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n    }\n};\nconst extendTailwindMerge = function(configExtension) {\n    for(var _len = arguments.length, createConfig = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        createConfig[_key - 1] = arguments[_key];\n    }\n    return typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(()=>mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\n};\nconst twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);\n //# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/kontakt/ContactForm.jsx":
/*!*****************************************!*\
  !*** ./src/app/kontakt/ContactForm.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_UnifiedButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/UnifiedButton */ \"(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ContactForm() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        message: '',\n        honeypot: ''\n    });\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [touched, setTouched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Validation functions\n    const validateField = (name, value)=>{\n        switch(name){\n            case 'name':\n                if (!value.trim()) return 'Imię jest wymagane';\n                if (value.trim().length < 2) return 'Imię musi mieć co najmniej 2 znaki';\n                if (!/^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\\s]+$/.test(value)) return 'Imię może zawierać tylko litery';\n                return '';\n            case 'email':\n                if (!value.trim()) return 'Email jest wymagany';\n                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                if (!emailRegex.test(value)) return 'Podaj prawidłowy adres email';\n                return '';\n            case 'message':\n                if (!value.trim()) return 'Wiadomość jest wymagana';\n                if (value.trim().length < 10) return 'Wiadomość musi mieć co najmniej 10 znaków';\n                if (value.trim().length > 1000) return 'Wiadomość nie może przekraczać 1000 znaków';\n                return '';\n            default:\n                return '';\n        }\n    };\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData({\n            ...formData,\n            [id]: value\n        });\n        // Real-time validation\n        if (touched[id]) {\n            const error = validateField(id, value);\n            setErrors((prev)=>({\n                    ...prev,\n                    [id]: error\n                }));\n        }\n    };\n    const handleBlur = (e)=>{\n        const { id, value } = e.target;\n        setTouched((prev)=>({\n                ...prev,\n                [id]: true\n            }));\n        const error = validateField(id, value);\n        setErrors((prev)=>({\n                ...prev,\n                [id]: error\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Honeypot check - jeśli wypełnione, to spam\n        if (formData.honeypot) {\n            return;\n        }\n        // Validate all fields\n        const newErrors = {};\n        Object.keys(formData).forEach((key)=>{\n            if (key !== 'honeypot') {\n                const error = validateField(key, formData[key]);\n                if (error) newErrors[key] = error;\n            }\n        });\n        setErrors(newErrors);\n        setTouched({\n            name: true,\n            email: true,\n            message: true\n        });\n        // If there are errors, don't submit\n        if (Object.keys(newErrors).length > 0) {\n            setStatus('Proszę poprawić błędy w formularzu');\n            setTimeout(()=>setStatus(''), 5000);\n            return;\n        }\n        setIsSubmitting(true);\n        setStatus('Wysyłanie...');\n        try {\n            const response = await fetch('https://api.web3forms.com/submit', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify({\n                    access_key: 'YOUR_WEB3FORMS_ACCESS_KEY',\n                    name: formData.name,\n                    email: formData.email,\n                    message: formData.message,\n                    subject: \"Nowa wiadomość z BAKASANA od \".concat(formData.name),\n                    from_name: 'BAKASANA',\n                    to_email: '<EMAIL>'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setStatus('Wiadomość wysłana. Dziękujemy, odpowiemy wkrótce.');\n                setFormData({\n                    name: '',\n                    email: '',\n                    message: '',\n                    honeypot: ''\n                });\n                setErrors({});\n                setTouched({});\n            } else {\n                throw new Error('Błąd wysyłania');\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setStatus('Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');\n        } finally{\n            setIsSubmitting(false);\n            setTimeout(()=>setStatus(''), 8000);\n        }\n    };\n    const socialLinks = [\n        {\n            href: \"https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr\",\n            label: \"Instagram\",\n            aria: \"Profil na Instagramie\"\n        },\n        {\n            href: \"https://www.facebook.com/p/Fly-with-bakasana-100077568306563/\",\n            label: \"Facebook\",\n            aria: \"Profil na Facebooku\"\n        },\n        {\n            href: \"mailto:<EMAIL>\",\n            label: \"Email\",\n            aria: \"Kontakt email\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-header mb-md\",\n                                children: \"Napisz do nas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-text opacity-80\",\n                                children: \"Każda wiadomość jest dla nas ważna\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"name\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Imię\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        className: \"w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 focus:outline-none \".concat(errors.name ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'),\n                                        placeholder: \"Twoje imię\",\n                                        \"aria-invalid\": errors.name ? 'true' : 'false',\n                                        \"aria-describedby\": errors.name ? 'name-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"name-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        role: \"alert\",\n                                        children: errors.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        className: \"w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 focus:outline-none \".concat(errors.email ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'),\n                                        placeholder: \"<EMAIL>\",\n                                        \"aria-invalid\": errors.email ? 'true' : 'false',\n                                        \"aria-describedby\": errors.email ? 'email-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"email-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        role: \"alert\",\n                                        children: errors.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"message\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: [\n                                            \"Wiadomość\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-stone/60 ml-2\",\n                                                children: [\n                                                    \"(\",\n                                                    formData.message.length,\n                                                    \"/1000)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"message\",\n                                        value: formData.message,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        rows: 6,\n                                        maxLength: 1000,\n                                        className: \"w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none focus:outline-none \".concat(errors.message ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'),\n                                        placeholder: \"Podziel się swoimi myślami...\",\n                                        \"aria-invalid\": errors.message ? 'true' : 'false',\n                                        \"aria-describedby\": errors.message ? 'message-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"message-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        role: \"alert\",\n                                        children: errors.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"honeypot\",\n                                name: \"honeypot\",\n                                value: formData.honeypot,\n                                onChange: handleChange,\n                                className: \"hidden\",\n                                tabIndex: \"-1\",\n                                autoComplete: \"off\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || Object.keys(errors).some((key)=>errors[key]),\n                                        className: \"btn-ghost btn-primary relative \".concat(isSubmitting || Object.keys(errors).some((key)=>errors[key]) ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-80'),\n                                        children: [\n                                            isSubmitting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute left-4 top-1/2 transform -translate-y-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: isSubmitting ? 'ml-6' : '',\n                                                children: isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-light mt-sm max-w-xs \".concat(status.includes('błąd') || status.includes('Proszę poprawić') ? 'text-red-500' : status.includes('wysłana') ? 'text-green-600' : 'text-charcoal/70'),\n                                        children: status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"section-header mb-md\",\n                                children: \"Znajdź nas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-text opacity-80 mb-lg\",\n                                children: \"Połączmy się w przestrzeni cyfrowej\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-md\",\n                        children: socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                \"aria-label\": link.aria,\n                                className: \"block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-light text-charcoal mb-2 tracking-wide text-lg\",\n                                        children: link.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-stone font-light\",\n                                        children: [\n                                            link.label === 'Instagram' && 'Codzienne inspiracje',\n                                            link.label === 'Facebook' && 'Społeczność BAKASANA',\n                                            link.label === 'Email' && 'Bezpośredni kontakt'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, link.label, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 284,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center lg:justify-start my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-sm text-charcoal-gold/60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-px bg-charcoal-gold/30\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg opacity-60\",\n                                    children: \"ॐ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-px bg-charcoal-gold/30\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-stone font-light italic tracking-wide\",\n                                children: '\"Każda podr\\xf3ż zaczyna się od jednego kroku...\"'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2\",\n                                children: \"Om Swastiastu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"4tfRqwkkktSIrSvJUas/ZSCi9RU=\");\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/kontakt/ContactForm.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PerformantWhatsApp.jsx":
/*!***********************************************!*\
  !*** ./src/components/PerformantWhatsApp.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Inline SVG WhatsApp icon - no external image requests\nconst WhatsAppIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { className = \"w-5 h-5\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 14,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n        lineNumber: 7,\n        columnNumber: 3\n    }, undefined);\n});\n_c = WhatsAppIcon;\nWhatsAppIcon.displayName = 'WhatsAppIcon';\n// Simplified WhatsApp component - minimal tracking\nconst PerformantWhatsApp = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s((param)=>{\n    let { className = '', size = 'md', message = 'Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?', variant = 'button' } = param;\n    _s();\n    const phoneNumber = '48606101523';\n    // Simplified analytics - only essential tracking\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PerformantWhatsApp.useCallback[handleClick]\": ()=>{\n            // GA4 event\n            if ( true && window.gtag) {\n                window.gtag('event', 'whatsapp_click', {\n                    event_category: 'engagement',\n                    event_label: 'whatsapp_contact',\n                    value: 1\n                });\n            }\n            // FB Pixel Contact event\n            if ( true && window.fbq) {\n                window.fbq('track', 'Contact');\n            }\n        }\n    }[\"PerformantWhatsApp.useCallback[handleClick]\"], []);\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-5 h-5',\n        lg: 'w-6 h-6'\n    };\n    const buttonClasses = {\n        sm: 'h-10 w-10',\n        md: 'h-12 w-12',\n        lg: 'h-14 w-14'\n    };\n    const whatsappUrl = \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodeURIComponent(message));\n    // Icon variant for navbar/footer\n    if (variant === 'icon') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: whatsappUrl,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            onClick: handleClick,\n            className: \"text-enterprise-brown hover:text-terra transition-colors duration-300 \".concat(className),\n            \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n                className: sizeClasses[size]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Floating variant for QuickCTA - Fixed z-index conflict\n    if (variant === 'floating') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 right-6 z-40\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: whatsappUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                onClick: handleClick,\n                className: \"\\n            bg-green-500 hover:bg-green-600 text-white\\n            w-14 h-14\\n            transition-all duration-300\\n            hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-500/50\\n            focus:ring-offset-2 shadow-lg hover:shadow-xl\\n            flex items-center justify-center\\n            animate-pulse hover:animate-none\\n            \".concat(className, \"\\n          \"),\n                \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default button\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: whatsappUrl,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        onClick: handleClick,\n        className: \"\\n        bg-enterprise-brown hover:bg-enterprise-brown/90 text-white \\n        \".concat(buttonClasses[size], \"\\n        transition-all duration-300 \\n        hover:scale-105 focus:outline-none focus:ring-2 focus:ring-enterprise-brown/50\\n        focus:ring-offset-2 shadow-lg hover:shadow-xl\\n        flex items-center justify-center\\n        \").concat(className, \"\\n      \"),\n        \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n            className: sizeClasses[size]\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n}, \"PRIOWs9bezaAbp8UlGmbaZMoYYA=\")), \"PRIOWs9bezaAbp8UlGmbaZMoYYA=\");\n_c2 = PerformantWhatsApp;\nPerformantWhatsApp.displayName = 'PerformantWhatsApp';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformantWhatsApp);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WhatsAppIcon\");\n$RefreshReg$(_c1, \"PerformantWhatsApp$memo\");\n$RefreshReg$(_c2, \"PerformantWhatsApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PerformantWhatsApp.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/UnifiedButton.jsx":
/*!*********************************************!*\
  !*** ./src/components/ui/UnifiedButton.jsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CTAButton: () => (/* binding */ CTAButton),\n/* harmony export */   GhostButton: () => (/* binding */ GhostButton),\n/* harmony export */   LinkButton: () => (/* binding */ LinkButton),\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton),\n/* harmony export */   \"default\": () => (/* binding */ UnifiedButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default,CTAButton,SecondaryButton,GhostButton,LinkButton auto */ \n\n\n/**\r\n * UnifiedButton - Ujednolicony system przycisków BAKASANA\r\n * Elegancja Old Money + Ciepły minimalizm\r\n */ const buttonVariants = {\n    // PRIMARY - Główne akcje (CTA)\n    primary: {\n        base: \"bg-enterprise-brown text-sanctuary border border-enterprise-brown\",\n        hover: \"hover:bg-terra hover:border-terra hover:shadow-elegant\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2\"\n    },\n    // SECONDARY - Drugie w hierarchii\n    secondary: {\n        base: \"bg-transparent text-enterprise-brown border border-enterprise-brown\",\n        hover: \"hover:bg-enterprise-brown hover:text-sanctuary hover:shadow-elegant\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2\"\n    },\n    // GHOST - Subtelne akcje\n    ghost: {\n        base: \"bg-transparent text-charcoal border-0\",\n        hover: \"hover:bg-whisper hover:text-enterprise-brown\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1\"\n    },\n    // MINIMAL - Ultra-subtelne\n    minimal: {\n        base: \"bg-transparent text-sage border-0 underline decoration-1 underline-offset-4\",\n        hover: \"hover:text-enterprise-brown hover:decoration-enterprise-brown\",\n        focus: \"focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1\"\n    }\n};\nconst sizeVariants = {\n    // All sizes meet WCAG 2.1 AA touch target requirements (44px minimum)\n    sm: \"px-6 py-3 text-xs tracking-[1px] min-h-[44px]\",\n    md: \"px-8 py-3.5 text-sm tracking-[1.2px] min-h-[48px]\",\n    lg: \"px-12 py-4 text-sm tracking-[1.5px] min-h-[52px]\",\n    xl: \"px-16 py-5 text-base tracking-[2px] min-h-[56px]\" // 56px height\n};\nfunction UnifiedButton(param) {\n    let { children, variant = 'primary', size = 'md', className = '', disabled = false, loading = false, as: Component = 'button', ...props } = param;\n    const variantStyles = buttonVariants[variant];\n    const sizeStyles = sizeVariants[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(// Base styles - Old Money elegance\n        \"inline-flex items-center justify-center font-inter font-light uppercase\", \"transition-all duration-300 ease-out\", \"focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\", \"transform hover:-translate-y-0.5 active:translate-y-0\", // Variant styles\n        variantStyles.base, variantStyles.hover, variantStyles.focus, // Size styles\n        sizeStyles, // Loading state\n        loading && \"opacity-70 cursor-wait\", // Custom className\n        className),\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = UnifiedButton;\n// Wyspecjalizowane warianty dla częstych przypadków użycia\nfunction CTAButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"primary\",\n        size: \"lg\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CTAButton;\nfunction SecondaryButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"secondary\",\n        size: \"md\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SecondaryButton;\nfunction GhostButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"ghost\",\n        size: \"md\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_c3 = GhostButton;\nfunction LinkButton(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedButton, {\n        variant: \"minimal\",\n        size: \"sm\",\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\ui\\\\UnifiedButton.jsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_c4 = LinkButton;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UnifiedButton\");\n$RefreshReg$(_c1, \"CTAButton\");\n$RefreshReg$(_c2, \"SecondaryButton\");\n$RefreshReg$(_c3, \"GhostButton\");\n$RefreshReg$(_c4, \"LinkButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/UnifiedButton.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.js":
/*!**************************!*\
  !*** ./src/lib/utils.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isInViewport: () => (/* binding */ isInViewport),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   lerp: () => (/* binding */ lerp),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_clsx_clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=clsx!=!clsx */ \"(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _barrel_optimize_names_twMerge_tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=twMerge!=!tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,_barrel_optimize_names_twMerge_tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,_barrel_optimize_names_clsx_clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Debounce function for performance optimization\r\n */ function debounce(func, wait) {\n    let timeout;\n    return function executedFunction() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}\n/**\r\n * Throttle function for performance optimization\r\n */ function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!inThrottle) {\n            func.apply(this, args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\r\n * Format price in Polish currency\r\n */ function formatPrice(price) {\n    return new Intl.NumberFormat('pl-PL', {\n        style: 'currency',\n        currency: 'PLN'\n    }).format(price);\n}\n/**\r\n * Validate email address\r\n */ function validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\r\n * Generate unique ID\r\n */ function generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n/**\r\n * Check if element is in viewport\r\n */ function isInViewport(element) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n}\n/**\r\n * Clamp number between min and max\r\n */ function clamp(value, min, max) {\n    return Math.min(Math.max(value, min), max);\n}\n/**\r\n * Linear interpolation\r\n */ function lerp(start, end, factor) {\n    return start + (end - start) * factor;\n}\n/**\r\n * Check if device is mobile\r\n */ function isMobile() {\n    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n}\n/**\r\n * Check if code is running in browser\r\n */ function isBrowser() {\n    return \"object\" !== 'undefined';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs":
/*!**************************************************************************!*\
  !*** __barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* reexport safe */ C_Users_david_Desktop_Projekty_bakasana_prod_bakasana_prod_node_modules_clsx_dist_clsx_mjs__WEBPACK_IMPORTED_MODULE_0__.clsx)\n/* harmony export */ });\n/* harmony import */ var C_Users_david_Desktop_Projekty_bakasana_prod_bakasana_prod_node_modules_clsx_dist_clsx_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/clsx/dist/clsx.mjs */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPWNsc3ghPSEuL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJha2FzYW5hX3Byb2RcXFxcYmFrYXNhbmFfcHJvZFxcXFxub2RlX21vZHVsZXNcXFxcY2xzeFxcXFxkaXN0XFxcXGNsc3gubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=clsx!=!./node_modules/clsx/dist/clsx.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);