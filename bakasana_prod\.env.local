# Podstawowe ustawienia strony - WŁASNA DOMENA
NEXT_PUBLIC_SITE_NAME=bakasana-travel.blog
NEXT_PUBLIC_SITE_DESCRIPTION=Odkryj piękno Bali z nami
NEXT_PUBLIC_BASE_URL=https://bakasana-travel.blog
NEXT_PUBLIC_SITE_URL=https://bakasana-travel.blog
NEXT_PUBLIC_SITE_IMAGE_URL=/og-image.jpg

# Google Analytics (opcjonalne)
NEXT_PUBLIC_GA_ID=G-M780DCS04D # Zaktualizuj na swój ID
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-M780DCS04D # Zaktualizuj na swój ID

# Google Search Console Verification
# UWAGA: Dodaj swój kod weryfikacyjny z Google Search Console
NEXT_PUBLIC_GOOGLE_VERIFICATION=your-google-verification-code

# Bundle analyzer (wyłączony w produkcji)
ANALYZE=false

# Środowisko
NODE_ENV=production

# Sanity CMS Configuration
# UWAGA: Zastąp 'your-project-id' swoim prawdziwym Project ID z Sanity
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-sanity-token

# Sanity Studio URL (po wdrożeniu)
NEXT_PUBLIC_SANITY_STUDIO_URL=https://bakasana-travel.sanity.studio

# BEZPIECZEŃSTWO - ZMIEŃ TE WARTOŚCI W PRODUKCJI!
# Admin panel - użyj silnego hasła
ADMIN_PASSWORD=BakasanaAdmin2024!SecurePass

# JWT Secret - wygeneruj losowy klucz dla produkcji
JWT_SECRET=a8f5f167f44f4964e6c998dee827110c

# ConvertKit API (opcjonalne) - USTAW PRAWDZIWE KLUCZE
CONVERTKIT_API_KEY=ck_your_real_api_key_here
CONVERTKIT_FORM_ID=**********************

# Email service (Resend, SendGrid, etc.) - USTAW PRAWDZIWY KLUCZ
RESEND_API_KEY=re_your_real_resend_key_here
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USER=resend
SMTP_PASS=re_your_real_resend_key_here