'use client';

import React, { useState } from 'react';

export default function BlogNewsletter() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the email to your newsletter service
    console.log('Newsletter signup:', email);
    setIsSubmitted(true);
    setEmail('');
    
    // Reset after 3 seconds
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  return (
    <div className="bg-gradient-to-r from-enterprise-brown/5 to-terra/5 py-16">
      <div className="max-w-4xl mx-auto px-hero-padding text-center">
        <div className="space-y-6">
          <div className="space-y-3">
            <h3 className="text-3xl font-cormorant text-charcoal">
              Bądź na bieżąco z naszymi podróżami
            </h3>
            <p className="text-charcoal/70 max-w-2xl mx-auto">
              Otrzy<PERSON><PERSON> najnowsze artykuły, inspiracje z podróży i ekskluzywne informacje o nadchodzących retreatach.
            </p>
          </div>

          {!isSubmitted ? (
            <form onSubmit={handleSubmit} className="max-w-md mx-auto">
              <div className="flex gap-3">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Twój adres email"
                  required
                  className="flex-1 px-4 py-3 bg-white border border-enterprise-brown/20 text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300"
                />
                <button
                  type="submit"
                  className="px-6 py-3 bg-enterprise-brown text-white hover:bg-enterprise-brown/90 transition-all duration-300 font-medium"
                >
                  Zapisz się
                </button>
              </div>
              <p className="text-xs text-charcoal/60 mt-3">
                Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.
              </p>
            </form>
          ) : (
            <div className="max-w-md mx-auto">
              <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded">
                <div className="flex items-center gap-2">
                  <span className="text-lg">✓</span>
                  <span>Dziękujemy! Sprawdź swoją skrzynkę email.</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center justify-center gap-6 text-sm text-charcoal/60">
            <div className="flex items-center gap-2">
              <span>📧</span>
              <span>Cotygodniowy newsletter</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🎁</span>
              <span>Ekskluzywne treści</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🚫</span>
              <span>Bez spamu</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
