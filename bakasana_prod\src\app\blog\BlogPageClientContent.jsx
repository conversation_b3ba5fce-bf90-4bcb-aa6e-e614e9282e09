'use client';

import Link from 'next/link';
import Image from 'next/image';
import React, { useMemo, useState } from 'react';
import BlogStats from '@/components/blog/BlogStats';
import BlogNewsletter from '@/components/blog/BlogNewsletter';

// Magazine Style Blog Card
const PostCard = ({ post, featured = false, className = '' }) => {
  if (!post) return null;

  const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';

  return (
    <article className={`${cardClass} ${className}`}>
      <Link href={`/blog/${post.slug || '#'}`} className="magazine-card-link">
        {/* IMAGE SECTION */}
        <div className="magazine-card-image">
          <div 
            className="magazine-image-bg"
            style={{
              backgroundImage: `url(${post.imageUrl || '/images/placeholder/image.jpg'})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <div className="magazine-image-overlay">
              <span className="magazine-category">
                {post.category || 'Zapiski z podróży'}
              </span>
            </div>
          </div>
        </div>

        {/* CONTENT SECTION */}
        <div className="magazine-card-content">
          <h3 className="magazine-card-title">
            {post.title || 'Bez tytułu'}
          </h3>

          <p className="magazine-card-excerpt">
            {post.excerpt || ''}
          </p>

          <div className="magazine-card-footer">
            <span className="magazine-read-more">Czytaj więcej</span>
            {post.readTime && (
              <span className="magazine-read-time">{post.readTime} min</span>
            )}
          </div>
        </div>
      </Link>
    </article>
  );
};

export default function BlogPageClientContent({ posts = [] }) {
  const [selectedCategory, setSelectedCategory] = useState('wszystkie');
  const [searchTerm, setSearchTerm] = useState('');

  const memoizedPosts = useMemo(() => {
    if (!Array.isArray(posts)) return [];
    return posts.filter(post => post && typeof post === 'object');
  }, [posts]);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = ['wszystkie', ...new Set(memoizedPosts.map(post => post.category).filter(Boolean))];
    return cats;
  }, [memoizedPosts]);

  // Filter posts based on category and search
  const filteredPosts = useMemo(() => {
    let filtered = memoizedPosts;

    if (selectedCategory !== 'wszystkie') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [memoizedPosts, selectedCategory, searchTerm]);

  return (
    <div className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Enhanced with Search */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>

          <h1 className="magazine-title">
            Zapiski z Podróży
          </h1>

          <p className="magazine-subtitle">
            Historie napisane sercem • Inspiracje z Azji • Praktyka jogi
          </p>

          {/* Search Bar */}
          <div className="mt-8 max-w-md mx-auto">
            <input
              type="text"
              placeholder="Szukaj artykułów..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 rounded-none text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300"
            />
          </div>

          <div className="magazine-meta">
            {filteredPosts.length} {filteredPosts.length === 1 ? 'artykuł' : 'artykułów'}
          </div>

          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* CATEGORY FILTER */}
      <section className="py-8 bg-white/50">
        <div className="max-w-6xl mx-auto px-hero-padding">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 text-sm font-light tracking-wide transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-enterprise-brown text-white'
                    : 'bg-white/80 text-charcoal hover:bg-enterprise-brown/10 border border-enterprise-brown/20'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* BLOG POSTS - Enhanced Layout */}
      <section className="magazine-content">
        {filteredPosts.length > 0 ? (
          <>
            {/* Featured Article - Large */}
            {filteredPosts[0] && (
              <div className="max-w-6xl mx-auto px-hero-padding mb-16">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div className="order-2 lg:order-1">
                    <div className="space-y-6">
                      <div className="flex items-center gap-3">
                        <span className="px-3 py-1 bg-enterprise-brown/10 text-enterprise-brown text-xs font-medium tracking-wide uppercase">
                          Wyróżniony
                        </span>
                        <span className="text-charcoal/60 text-sm">
                          {filteredPosts[0].category}
                        </span>
                      </div>

                      <h2 className="text-4xl lg:text-5xl font-cormorant text-charcoal leading-tight">
                        {filteredPosts[0].title}
                      </h2>

                      <p className="text-lg text-charcoal/80 leading-relaxed">
                        {filteredPosts[0].excerpt}
                      </p>

                      <div className="flex items-center gap-4 text-sm text-charcoal/60">
                        <span>{filteredPosts[0].author}</span>
                        <span>•</span>
                        <span>{filteredPosts[0].readTime || '5'} min</span>
                        <span>•</span>
                        <span>{new Date(filteredPosts[0].date).toLocaleDateString('pl-PL')}</span>
                      </div>

                      <Link
                        href={`/blog/${filteredPosts[0].slug}`}
                        className="inline-flex items-center gap-2 text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300 font-medium"
                      >
                        Czytaj artykuł
                        <span className="text-lg">→</span>
                      </Link>
                    </div>
                  </div>

                  <div className="order-1 lg:order-2">
                    <div className="aspect-[4/3] relative overflow-hidden bg-charcoal/5">
                      <Image
                        src={filteredPosts[0].imageUrl || '/images/placeholder/image.jpg'}
                        alt={filteredPosts[0].imageAlt || filteredPosts[0].title}
                        fill
                        className="object-cover transition-transform duration-700 hover:scale-105"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Articles Grid */}
            <div className="max-w-6xl mx-auto px-hero-padding">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.slice(1).map((post, index) => (
                  <article key={post.slug || index} className="group">
                    <Link href={`/blog/${post.slug}`} className="block">
                      <div className="space-y-4">
                        {/* Image */}
                        <div className="aspect-[16/10] relative overflow-hidden bg-charcoal/5">
                          <Image
                            src={post.imageUrl || '/images/placeholder/image.jpg'}
                            alt={post.imageAlt || post.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-105"
                          />
                          <div className="absolute top-4 left-4">
                            <span className="px-2 py-1 bg-white/90 backdrop-blur-sm text-charcoal text-xs font-medium">
                              {post.category}
                            </span>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="space-y-3">
                          <h3 className="text-xl font-cormorant text-charcoal leading-tight group-hover:text-enterprise-brown transition-colors duration-300">
                            {post.title}
                          </h3>

                          <p className="text-charcoal/70 text-sm leading-relaxed line-clamp-3">
                            {post.excerpt}
                          </p>

                          <div className="flex items-center justify-between text-xs text-charcoal/60">
                            <span>{new Date(post.date).toLocaleDateString('pl-PL')}</span>
                            <span>{post.readTime || '5'} min</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </article>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="max-w-2xl mx-auto px-hero-padding py-16 text-center">
            <div className="space-y-4">
              <h3 className="text-2xl font-cormorant text-charcoal">
                {searchTerm ? 'Nie znaleziono artykułów' : 'Wkrótce więcej treści'}
              </h3>
              <p className="text-charcoal/70">
                {searchTerm
                  ? `Brak wyników dla "${searchTerm}". Spróbuj innych słów kluczowych.`
                  : 'Pracujemy nad nowymi inspirującymi artykułami'
                }
              </p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300"
                >
                  Wyczyść wyszukiwanie
                </button>
              )}
            </div>
          </div>
        )}
      </section>

      {/* COMMUNITY SECTION - BAKASANA Standards */}
      <section className="container">
        <div className="text-center space-y-lg max-w-3xl mx-auto">
          <div className="space-y-md">
            <h3 className="section-header">
              Bądź na bieżąco
            </h3>

            <p className="body-text opacity-80">
              Otrzymuj najnowsze artykuły i inspiracje z duchowych podróży
            </p>
          </div>

          {/* SACRED DIVIDER */}
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-sm text-charcoal-gold/60">
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
              <span className="text-lg opacity-60">ॐ</span>
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
            </div>
          </div>

          {/* CONTACT LINKS - Ghost buttons */}
          <div className="flex flex-col sm:flex-row gap-lg justify-center items-center">
            <a
              href="https://www.instagram.com/fly_with_bakasana"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost"
            >
              Instagram
            </a>
            <a
              href="mailto:<EMAIL>"
              className="btn-ghost"
            >
              Email
            </a>
          </div>

          <div className="pt-8">
            <p className="text-sm text-stone font-light italic tracking-wide">
              "Każda historia ma swoją moc..."
            </p>
            <p className="text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2">
              Om Swastiastu
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}