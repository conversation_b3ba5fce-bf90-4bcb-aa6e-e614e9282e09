"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":
/*!************************************************!*\
  !*** ./src/app/blog/BlogPageClientContent.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPageClientContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Magazine Style Blog Card\nconst PostCard = (param)=>{\n    let { post, featured = false, className = '' } = param;\n    if (!post) return null;\n    const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"\".concat(cardClass, \" \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: \"/blog/\".concat(post.slug || '#'),\n            className: \"magazine-card-link\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-image-bg\",\n                        style: {\n                            backgroundImage: \"url(\".concat(post.imageUrl || '/images/placeholder/image.jpg', \")\"),\n                            backgroundSize: 'cover',\n                            backgroundPosition: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-image-overlay\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"magazine-category\",\n                                children: post.category || 'Zapiski z podróży'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"magazine-card-title\",\n                            children: post.title || 'Bez tytułu'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-card-excerpt\",\n                            children: post.excerpt || ''\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-card-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-more\",\n                                    children: \"Czytaj więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                post.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-time\",\n                                    children: [\n                                        post.readTime,\n                                        \" min\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PostCard;\nfunction BlogPageClientContent(param) {\n    let { posts = [] } = param;\n    _s();\n    const memoizedPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[memoizedPosts]\": ()=>{\n            if (!Array.isArray(posts)) return [];\n            return posts.filter({\n                \"BlogPageClientContent.useMemo[memoizedPosts]\": (post)=>post && typeof post === 'object'\n            }[\"BlogPageClientContent.useMemo[memoizedPosts]\"]);\n        }\n    }[\"BlogPageClientContent.useMemo[memoizedPosts]\"], [\n        posts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-sanctuary min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-hero\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"magazine-title\",\n                            children: \"Zapiski\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-subtitle\",\n                            children: \"Historie napisane sercem\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-meta\",\n                            children: \"Bali • Sri Lanka • Wewnętrzne podr\\xf3że\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-content\",\n                children: memoizedPosts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-grid\",\n                    children: [\n                        memoizedPosts[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"magazine-featured\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                post: memoizedPosts[0],\n                                featured: true,\n                                className: \"featured-card\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 92,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-secondary\",\n                            children: memoizedPosts.slice(1, 3).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"magazine-secondary-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                        post: post,\n                                        className: \"secondary-card\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 19\n                                    }, this)\n                                }, \"secondary-\".concat((post === null || post === void 0 ? void 0 : post.slug) || index), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        memoizedPosts.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-grid-small\",\n                            children: memoizedPosts.slice(3).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"magazine-small-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                        post: post,\n                                        className: \"small-card\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 21\n                                    }, this)\n                                }, \"small-\".concat((post === null || post === void 0 ? void 0 : post.slug) || index), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-empty\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-empty-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"magazine-empty-title\",\n                                children: \"Wkr\\xf3tce więcej treści\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"magazine-empty-text\",\n                                children: \"Pracujemy nad nowymi inspirującymi artykułami\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-lg max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-header\",\n                                    children: \"Bądź na bieżąco\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-text opacity-80\",\n                                    children: \"Otrzymuj najnowsze artykuły i inspiracje z duchowych podr\\xf3ży\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center my-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-sm text-charcoal-gold/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg opacity-60\",\n                                        children: \"ॐ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-lg justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.instagram.com/fly_with_bakasana\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-ghost\",\n                                    children: \"Instagram\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"btn-ghost\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-stone font-light italic tracking-wide\",\n                                    children: '\"Każda historia ma swoją moc...\"'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2\",\n                                    children: \"Om Swastiastu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPageClientContent, \"pjo94WGpnFLY1SuJ4YQlQvoxhuk=\");\n_c1 = BlogPageClientContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"PostCard\");\n$RefreshReg$(_c1, \"BlogPageClientContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx\n"));

/***/ })

});