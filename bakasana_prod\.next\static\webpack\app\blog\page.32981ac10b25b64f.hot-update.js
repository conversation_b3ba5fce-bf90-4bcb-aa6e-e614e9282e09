"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":
/*!************************************************!*\
  !*** ./src/app/blog/BlogPageClientContent.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPageClientContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_blog_BlogStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/blog/BlogStats */ \"(app-pages-browser)/./src/components/blog/BlogStats.jsx\");\n/* harmony import */ var _components_blog_BlogNewsletter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/blog/BlogNewsletter */ \"(app-pages-browser)/./src/components/blog/BlogNewsletter.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Magazine Style Blog Card\nconst PostCard = (param)=>{\n    let { post, featured = false, className = '' } = param;\n    if (!post) return null;\n    const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"\".concat(cardClass, \" \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: \"/blog/\".concat(post.slug || '#'),\n            className: \"magazine-card-link\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-image-bg\",\n                        style: {\n                            backgroundImage: \"url(\".concat(post.imageUrl || '/images/placeholder/image.jpg', \")\"),\n                            backgroundSize: 'cover',\n                            backgroundPosition: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-image-overlay\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"magazine-category\",\n                                children: post.category || 'Zapiski z podróży'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"magazine-card-title\",\n                            children: post.title || 'Bez tytułu'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-card-excerpt\",\n                            children: post.excerpt || ''\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-card-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-more\",\n                                    children: \"Czytaj więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined),\n                                post.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-time\",\n                                    children: [\n                                        post.readTime,\n                                        \" min\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PostCard;\nfunction BlogPageClientContent(param) {\n    let { posts = [] } = param;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('wszystkie');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const memoizedPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[memoizedPosts]\": ()=>{\n            if (!Array.isArray(posts)) return [];\n            return posts.filter({\n                \"BlogPageClientContent.useMemo[memoizedPosts]\": (post)=>post && typeof post === 'object'\n            }[\"BlogPageClientContent.useMemo[memoizedPosts]\"]);\n        }\n    }[\"BlogPageClientContent.useMemo[memoizedPosts]\"], [\n        posts\n    ]);\n    // Get unique categories\n    const categories = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[categories]\": ()=>{\n            const cats = [\n                'wszystkie',\n                ...new Set(memoizedPosts.map({\n                    \"BlogPageClientContent.useMemo[categories]\": (post)=>post.category\n                }[\"BlogPageClientContent.useMemo[categories]\"]).filter(Boolean))\n            ];\n            return cats;\n        }\n    }[\"BlogPageClientContent.useMemo[categories]\"], [\n        memoizedPosts\n    ]);\n    // Filter posts based on category and search\n    const filteredPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[filteredPosts]\": ()=>{\n            let filtered = memoizedPosts;\n            if (selectedCategory !== 'wszystkie') {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>post.category === selectedCategory\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            if (searchTerm) {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>{\n                        var _post_title, _post_excerpt, _post_tags;\n                        return ((_post_title = post.title) === null || _post_title === void 0 ? void 0 : _post_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_excerpt = post.excerpt) === null || _post_excerpt === void 0 ? void 0 : _post_excerpt.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_tags = post.tags) === null || _post_tags === void 0 ? void 0 : _post_tags.some({\n                            \"BlogPageClientContent.useMemo[filteredPosts]\": (tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())\n                        }[\"BlogPageClientContent.useMemo[filteredPosts]\"]));\n                    }\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            return filtered;\n        }\n    }[\"BlogPageClientContent.useMemo[filteredPosts]\"], [\n        memoizedPosts,\n        selectedCategory,\n        searchTerm\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-sanctuary min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-hero\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"magazine-title\",\n                            children: \"Zapiski z Podr\\xf3ży\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-subtitle\",\n                            children: \"Historie napisane sercem • Inspiracje z Azji • Praktyka jogi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 max-w-md mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Szukaj artykuł\\xf3w...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 rounded-none text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-meta\",\n                            children: [\n                                filteredPosts.length,\n                                \" \",\n                                filteredPosts.length === 1 ? 'artykuł' : 'artykułów'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 bg-white/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-hero-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-3\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-2 text-sm font-light tracking-wide transition-all duration-300 \".concat(selectedCategory === category ? 'bg-enterprise-brown text-white' : 'bg-white/80 text-charcoal hover:bg-enterprise-brown/10 border border-enterprise-brown/20'),\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-content\",\n                children: filteredPosts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        filteredPosts[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-hero-padding mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-2 lg:order-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-3 py-1 bg-enterprise-brown/10 text-enterprise-brown text-xs font-medium tracking-wide uppercase\",\n                                                            children: \"Wyr\\xf3żniony\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-charcoal/60 text-sm\",\n                                                            children: filteredPosts[0].category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-4xl lg:text-5xl font-cormorant text-charcoal leading-tight\",\n                                                    children: filteredPosts[0].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-charcoal/80 leading-relaxed\",\n                                                    children: filteredPosts[0].excerpt\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 text-sm text-charcoal/60\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: filteredPosts[0].author\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                filteredPosts[0].readTime || '5',\n                                                                \" min\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: new Date(filteredPosts[0].date).toLocaleDateString('pl-PL')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/blog/\".concat(filteredPosts[0].slug),\n                                                    className: \"inline-flex items-center gap-2 text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300 font-medium\",\n                                                    children: [\n                                                        \"Czytaj artykuł\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: \"→\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"order-1 lg:order-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/3] relative overflow-hidden bg-charcoal/5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: filteredPosts[0].imageUrl || '/images/placeholder/image.jpg',\n                                                alt: filteredPosts[0].imageAlt || filteredPosts[0].title,\n                                                fill: true,\n                                                className: \"object-cover transition-transform duration-700 hover:scale-105\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 154,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 153,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-hero-padding\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: filteredPosts.slice(1).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                        className: \"group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/blog/\".concat(post.slug),\n                                            className: \"block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-[16/10] relative overflow-hidden bg-charcoal/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: post.imageUrl || '/images/placeholder/image.jpg',\n                                                                alt: post.imageAlt || post.title,\n                                                                fill: true,\n                                                                className: \"object-cover transition-transform duration-500 group-hover:scale-105\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 left-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-white/90 backdrop-blur-sm text-charcoal text-xs font-medium\",\n                                                                    children: post.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-cormorant text-charcoal leading-tight group-hover:text-enterprise-brown transition-colors duration-300\",\n                                                                children: post.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-charcoal/70 text-sm leading-relaxed line-clamp-3\",\n                                                                children: post.excerpt\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs text-charcoal/60\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(post.date).toLocaleDateString('pl-PL')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            post.readTime || '5',\n                                                                            \" min\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, post.slug || index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-hero-padding py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-cormorant text-charcoal\",\n                                children: searchTerm ? 'Nie znaleziono artykułów' : 'Wkrótce więcej treści'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-charcoal/70\",\n                                children: searchTerm ? 'Brak wynik\\xf3w dla \"'.concat(searchTerm, '\". Spr\\xf3buj innych sł\\xf3w kluczowych.') : 'Pracujemy nad nowymi inspirującymi artykułami'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this),\n                            searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSearchTerm(''),\n                                className: \"text-enterprise-brown hover:text-enterprise-brown/80 transition-colors duration-300\",\n                                children: \"Wyczyść wyszukiwanie\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 263,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 252,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-lg max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-header\",\n                                    children: \"Bądź na bieżąco\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-text opacity-80\",\n                                    children: \"Otrzymuj najnowsze artykuły i inspiracje z duchowych podr\\xf3ży\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center my-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-sm text-charcoal-gold/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg opacity-60\",\n                                        children: \"ॐ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-lg justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.instagram.com/fly_with_bakasana\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-ghost\",\n                                    children: \"Instagram\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"btn-ghost\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-stone font-light italic tracking-wide\",\n                                    children: '\"Każda historia ma swoją moc...\"'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2\",\n                                    children: \"Om Swastiastu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPageClientContent, \"lo2uijBVUg9EXKtYRzlq39aa0Ko=\");\n_c1 = BlogPageClientContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"PostCard\");\n$RefreshReg$(_c1, \"BlogPageClientContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYmxvZy9CbG9nUGFnZUNsaWVudENvbnRlbnQuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTZCO0FBQ0U7QUFDa0I7QUFDRztBQUNVO0FBRTlELDJCQUEyQjtBQUMzQixNQUFNTyxXQUFXO1FBQUMsRUFBRUMsSUFBSSxFQUFFQyxXQUFXLEtBQUssRUFBRUMsWUFBWSxFQUFFLEVBQUU7SUFDMUQsSUFBSSxDQUFDRixNQUFNLE9BQU87SUFFbEIsTUFBTUcsWUFBWUYsV0FBVywyQkFBMkI7SUFFeEQscUJBQ0UsOERBQUNHO1FBQVFGLFdBQVcsR0FBZ0JBLE9BQWJDLFdBQVUsS0FBYSxPQUFWRDtrQkFDbEMsNEVBQUNWLGtEQUFJQTtZQUFDYSxNQUFNLFNBQTBCLE9BQWpCTCxLQUFLTSxJQUFJLElBQUk7WUFBT0osV0FBVTs7OEJBRWpELDhEQUFDSztvQkFBSUwsV0FBVTs4QkFDYiw0RUFBQ0s7d0JBQ0NMLFdBQVU7d0JBQ1ZNLE9BQU87NEJBQ0xDLGlCQUFpQixPQUF3RCxPQUFqRFQsS0FBS1UsUUFBUSxJQUFJLGlDQUFnQzs0QkFDekVDLGdCQUFnQjs0QkFDaEJDLG9CQUFvQjt3QkFDdEI7a0NBRUEsNEVBQUNMOzRCQUFJTCxXQUFVO3NDQUNiLDRFQUFDVztnQ0FBS1gsV0FBVTswQ0FDYkYsS0FBS2MsUUFBUSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTzFCLDhEQUFDUDtvQkFBSUwsV0FBVTs7c0NBQ2IsOERBQUNhOzRCQUFHYixXQUFVO3NDQUNYRixLQUFLZ0IsS0FBSyxJQUFJOzs7Ozs7c0NBR2pCLDhEQUFDQzs0QkFBRWYsV0FBVTtzQ0FDVkYsS0FBS2tCLE9BQU8sSUFBSTs7Ozs7O3NDQUduQiw4REFBQ1g7NEJBQUlMLFdBQVU7OzhDQUNiLDhEQUFDVztvQ0FBS1gsV0FBVTs4Q0FBcUI7Ozs7OztnQ0FDcENGLEtBQUttQixRQUFRLGtCQUNaLDhEQUFDTjtvQ0FBS1gsV0FBVTs7d0NBQXNCRixLQUFLbUIsUUFBUTt3Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2xFO0tBOUNNcEI7QUFnRFMsU0FBU3FCLHNCQUFzQixLQUFjO1FBQWQsRUFBRUMsUUFBUSxFQUFFLEVBQUUsR0FBZDs7SUFDNUMsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHM0IsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDNEIsWUFBWUMsY0FBYyxHQUFHN0IsK0NBQVFBLENBQUM7SUFFN0MsTUFBTThCLGdCQUFnQi9CLDhDQUFPQTt3REFBQztZQUM1QixJQUFJLENBQUNnQyxNQUFNQyxPQUFPLENBQUNQLFFBQVEsT0FBTyxFQUFFO1lBQ3BDLE9BQU9BLE1BQU1RLE1BQU07Z0VBQUM3QixDQUFBQSxPQUFRQSxRQUFRLE9BQU9BLFNBQVM7O1FBQ3REO3VEQUFHO1FBQUNxQjtLQUFNO0lBRVYsd0JBQXdCO0lBQ3hCLE1BQU1TLGFBQWFuQyw4Q0FBT0E7cURBQUM7WUFDekIsTUFBTW9DLE9BQU87Z0JBQUM7bUJBQWdCLElBQUlDLElBQUlOLGNBQWNPLEdBQUc7aUVBQUNqQyxDQUFBQSxPQUFRQSxLQUFLYyxRQUFRO2dFQUFFZSxNQUFNLENBQUNLO2FBQVU7WUFDaEcsT0FBT0g7UUFDVDtvREFBRztRQUFDTDtLQUFjO0lBRWxCLDRDQUE0QztJQUM1QyxNQUFNUyxnQkFBZ0J4Qyw4Q0FBT0E7d0RBQUM7WUFDNUIsSUFBSXlDLFdBQVdWO1lBRWYsSUFBSUoscUJBQXFCLGFBQWE7Z0JBQ3BDYyxXQUFXQSxTQUFTUCxNQUFNO29FQUFDN0IsQ0FBQUEsT0FBUUEsS0FBS2MsUUFBUSxLQUFLUTs7WUFDdkQ7WUFFQSxJQUFJRSxZQUFZO2dCQUNkWSxXQUFXQSxTQUFTUCxNQUFNO29FQUFDN0IsQ0FBQUE7NEJBQ3pCQSxhQUNBQSxlQUNBQTsrQkFGQUEsRUFBQUEsY0FBQUEsS0FBS2dCLEtBQUssY0FBVmhCLGtDQUFBQSxZQUFZcUMsV0FBVyxHQUFHQyxRQUFRLENBQUNkLFdBQVdhLFdBQVcsVUFDekRyQyxnQkFBQUEsS0FBS2tCLE9BQU8sY0FBWmxCLG9DQUFBQSxjQUFjcUMsV0FBVyxHQUFHQyxRQUFRLENBQUNkLFdBQVdhLFdBQVcsVUFDM0RyQyxhQUFBQSxLQUFLdUMsSUFBSSxjQUFUdkMsaUNBQUFBLFdBQVd3QyxJQUFJOzRFQUFDQyxDQUFBQSxNQUFPQSxJQUFJSixXQUFXLEdBQUdDLFFBQVEsQ0FBQ2QsV0FBV2EsV0FBVzs7OztZQUU1RTtZQUVBLE9BQU9EO1FBQ1Q7dURBQUc7UUFBQ1Y7UUFBZUo7UUFBa0JFO0tBQVc7SUFFaEQscUJBQ0UsOERBQUNqQjtRQUFJTCxXQUFVOzswQkFFYiw4REFBQ3dDO2dCQUFReEMsV0FBVTswQkFDakIsNEVBQUNLO29CQUFJTCxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUlMLFdBQVU7Ozs7OztzQ0FFZiw4REFBQ3lDOzRCQUFHekMsV0FBVTtzQ0FBaUI7Ozs7OztzQ0FJL0IsOERBQUNlOzRCQUFFZixXQUFVO3NDQUFvQjs7Ozs7O3NDQUtqQyw4REFBQ0s7NEJBQUlMLFdBQVU7c0NBQ2IsNEVBQUMwQztnQ0FDQ0MsTUFBSztnQ0FDTEMsYUFBWTtnQ0FDWkMsT0FBT3ZCO2dDQUNQd0IsVUFBVSxDQUFDQyxJQUFNeEIsY0FBY3dCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnQ0FDN0M3QyxXQUFVOzs7Ozs7Ozs7OztzQ0FJZCw4REFBQ0s7NEJBQUlMLFdBQVU7O2dDQUNaaUMsY0FBY2dCLE1BQU07Z0NBQUM7Z0NBQUVoQixjQUFjZ0IsTUFBTSxLQUFLLElBQUksWUFBWTs7Ozs7OztzQ0FHbkUsOERBQUM1Qzs0QkFBSUwsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS25CLDhEQUFDd0M7Z0JBQVF4QyxXQUFVOzBCQUNqQiw0RUFBQ0s7b0JBQUlMLFdBQVU7OEJBQ2IsNEVBQUNLO3dCQUFJTCxXQUFVO2tDQUNaNEIsV0FBV0csR0FBRyxDQUFDLENBQUNuQix5QkFDZiw4REFBQ3NDO2dDQUVDQyxTQUFTLElBQU05QixvQkFBb0JUO2dDQUNuQ1osV0FBVywwRUFJVixPQUhDb0IscUJBQXFCUixXQUNqQixtQ0FDQTswQ0FHTEE7K0JBUklBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFnQmYsOERBQUM0QjtnQkFBUXhDLFdBQVU7MEJBQ2hCaUMsY0FBY2dCLE1BQU0sR0FBRyxrQkFDdEI7O3dCQUVHaEIsYUFBYSxDQUFDLEVBQUUsa0JBQ2YsOERBQUM1Qjs0QkFBSUwsV0FBVTtzQ0FDYiw0RUFBQ0s7Z0NBQUlMLFdBQVU7O2tEQUNiLDhEQUFDSzt3Q0FBSUwsV0FBVTtrREFDYiw0RUFBQ0s7NENBQUlMLFdBQVU7OzhEQUNiLDhEQUFDSztvREFBSUwsV0FBVTs7c0VBQ2IsOERBQUNXOzREQUFLWCxXQUFVO3NFQUFxRzs7Ozs7O3NFQUdySCw4REFBQ1c7NERBQUtYLFdBQVU7c0VBQ2JpQyxhQUFhLENBQUMsRUFBRSxDQUFDckIsUUFBUTs7Ozs7Ozs7Ozs7OzhEQUk5Qiw4REFBQ3dDO29EQUFHcEQsV0FBVTs4REFDWGlDLGFBQWEsQ0FBQyxFQUFFLENBQUNuQixLQUFLOzs7Ozs7OERBR3pCLDhEQUFDQztvREFBRWYsV0FBVTs4REFDVmlDLGFBQWEsQ0FBQyxFQUFFLENBQUNqQixPQUFPOzs7Ozs7OERBRzNCLDhEQUFDWDtvREFBSUwsV0FBVTs7c0VBQ2IsOERBQUNXO3NFQUFNc0IsYUFBYSxDQUFDLEVBQUUsQ0FBQ29CLE1BQU07Ozs7OztzRUFDOUIsOERBQUMxQztzRUFBSzs7Ozs7O3NFQUNOLDhEQUFDQTs7Z0VBQU1zQixhQUFhLENBQUMsRUFBRSxDQUFDaEIsUUFBUSxJQUFJO2dFQUFJOzs7Ozs7O3NFQUN4Qyw4REFBQ047c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7c0VBQU0sSUFBSTJDLEtBQUtyQixhQUFhLENBQUMsRUFBRSxDQUFDc0IsSUFBSSxFQUFFQyxrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7OzhEQUc1RCw4REFBQ2xFLGtEQUFJQTtvREFDSGEsTUFBTSxTQUErQixPQUF0QjhCLGFBQWEsQ0FBQyxFQUFFLENBQUM3QixJQUFJO29EQUNwQ0osV0FBVTs7d0RBQ1g7c0VBRUMsOERBQUNXOzREQUFLWCxXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLaEMsOERBQUNLO3dDQUFJTCxXQUFVO2tEQUNiLDRFQUFDSzs0Q0FBSUwsV0FBVTtzREFDYiw0RUFBQ1Qsa0RBQUtBO2dEQUNKa0UsS0FBS3hCLGFBQWEsQ0FBQyxFQUFFLENBQUN6QixRQUFRLElBQUk7Z0RBQ2xDa0QsS0FBS3pCLGFBQWEsQ0FBQyxFQUFFLENBQUMwQixRQUFRLElBQUkxQixhQUFhLENBQUMsRUFBRSxDQUFDbkIsS0FBSztnREFDeEQ4QyxJQUFJO2dEQUNKNUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVN0Qiw4REFBQ0s7NEJBQUlMLFdBQVU7c0NBQ2IsNEVBQUNLO2dDQUFJTCxXQUFVOzBDQUNaaUMsY0FBYzRCLEtBQUssQ0FBQyxHQUFHOUIsR0FBRyxDQUFDLENBQUNqQyxNQUFNZ0Usc0JBQ2pDLDhEQUFDNUQ7d0NBQWlDRixXQUFVO2tEQUMxQyw0RUFBQ1Ysa0RBQUlBOzRDQUFDYSxNQUFNLFNBQW1CLE9BQVZMLEtBQUtNLElBQUk7NENBQUlKLFdBQVU7c0RBQzFDLDRFQUFDSztnREFBSUwsV0FBVTs7a0VBRWIsOERBQUNLO3dEQUFJTCxXQUFVOzswRUFDYiw4REFBQ1Qsa0RBQUtBO2dFQUNKa0UsS0FBSzNELEtBQUtVLFFBQVEsSUFBSTtnRUFDdEJrRCxLQUFLNUQsS0FBSzZELFFBQVEsSUFBSTdELEtBQUtnQixLQUFLO2dFQUNoQzhDLElBQUk7Z0VBQ0o1RCxXQUFVOzs7Ozs7MEVBRVosOERBQUNLO2dFQUFJTCxXQUFVOzBFQUNiLDRFQUFDVztvRUFBS1gsV0FBVTs4RUFDYkYsS0FBS2MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTXBCLDhEQUFDUDt3REFBSUwsV0FBVTs7MEVBQ2IsOERBQUNhO2dFQUFHYixXQUFVOzBFQUNYRixLQUFLZ0IsS0FBSzs7Ozs7OzBFQUdiLDhEQUFDQztnRUFBRWYsV0FBVTswRUFDVkYsS0FBS2tCLE9BQU87Ozs7OzswRUFHZiw4REFBQ1g7Z0VBQUlMLFdBQVU7O2tGQUNiLDhEQUFDVztrRkFBTSxJQUFJMkMsS0FBS3hELEtBQUt5RCxJQUFJLEVBQUVDLGtCQUFrQixDQUFDOzs7Ozs7a0ZBQzlDLDhEQUFDN0M7OzRFQUFNYixLQUFLbUIsUUFBUSxJQUFJOzRFQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBOUJ4Qm5CLEtBQUtNLElBQUksSUFBSTBEOzs7Ozs7Ozs7Ozs7Ozs7O2lEQXlDbkMsOERBQUN6RDtvQkFBSUwsV0FBVTs4QkFDYiw0RUFBQ0s7d0JBQUlMLFdBQVU7OzBDQUNiLDhEQUFDYTtnQ0FBR2IsV0FBVTswQ0FDWHNCLGFBQWEsNkJBQTZCOzs7Ozs7MENBRTdDLDhEQUFDUDtnQ0FBRWYsV0FBVTswQ0FDVnNCLGFBQ0csd0JBQWdDLE9BQVhBLFlBQVcsOENBQ2hDOzs7Ozs7NEJBR0xBLDRCQUNDLDhEQUFDNEI7Z0NBQ0NDLFNBQVMsSUFBTTVCLGNBQWM7Z0NBQzdCdkIsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVWCw4REFBQ3dDO2dCQUFReEMsV0FBVTswQkFDakIsNEVBQUNLO29CQUFJTCxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUlMLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FBR2IsV0FBVTs4Q0FBaUI7Ozs7Ozs4Q0FJL0IsOERBQUNlO29DQUFFZixXQUFVOzhDQUF1Qjs7Ozs7Ozs7Ozs7O3NDQU10Qyw4REFBQ0s7NEJBQUlMLFdBQVU7c0NBQ2IsNEVBQUNLO2dDQUFJTCxXQUFVOztrREFDYiw4REFBQ0s7d0NBQUlMLFdBQVU7Ozs7OztrREFDZiw4REFBQ1c7d0NBQUtYLFdBQVU7a0RBQXFCOzs7Ozs7a0RBQ3JDLDhEQUFDSzt3Q0FBSUwsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS25CLDhEQUFDSzs0QkFBSUwsV0FBVTs7OENBQ2IsOERBQUMrRDtvQ0FDQzVELE1BQUs7b0NBQ0w2QyxRQUFPO29DQUNQZ0IsS0FBSTtvQ0FDSmhFLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQytEO29DQUNDNUQsTUFBSztvQ0FDTEgsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQUtILDhEQUFDSzs0QkFBSUwsV0FBVTs7OENBQ2IsOERBQUNlO29DQUFFZixXQUFVOzhDQUFxRDs7Ozs7OzhDQUdsRSw4REFBQ2U7b0NBQUVmLFdBQVU7OENBQXFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVE5RjtHQTdRd0JrQjtNQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxkYXZpZFxcRGVza3RvcFxcUHJvamVrdHlcXGJha2FzYW5hX3Byb2RcXGJha2FzYW5hX3Byb2RcXHNyY1xcYXBwXFxibG9nXFxCbG9nUGFnZUNsaWVudENvbnRlbnQuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJztcbmltcG9ydCBSZWFjdCwgeyB1c2VNZW1vLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBCbG9nU3RhdHMgZnJvbSAnQC9jb21wb25lbnRzL2Jsb2cvQmxvZ1N0YXRzJztcbmltcG9ydCBCbG9nTmV3c2xldHRlciBmcm9tICdAL2NvbXBvbmVudHMvYmxvZy9CbG9nTmV3c2xldHRlcic7XG5cbi8vIE1hZ2F6aW5lIFN0eWxlIEJsb2cgQ2FyZFxuY29uc3QgUG9zdENhcmQgPSAoeyBwb3N0LCBmZWF0dXJlZCA9IGZhbHNlLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIGlmICghcG9zdCkgcmV0dXJuIG51bGw7XG5cbiAgY29uc3QgY2FyZENsYXNzID0gZmVhdHVyZWQgPyAnbWFnYXppbmUtY2FyZC1mZWF0dXJlZCcgOiAnbWFnYXppbmUtY2FyZCc7XG5cbiAgcmV0dXJuIChcbiAgICA8YXJ0aWNsZSBjbGFzc05hbWU9e2Ake2NhcmRDbGFzc30gJHtjbGFzc05hbWV9YH0+XG4gICAgICA8TGluayBocmVmPXtgL2Jsb2cvJHtwb3N0LnNsdWcgfHwgJyMnfWB9IGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWNhcmQtbGlua1wiPlxuICAgICAgICB7LyogSU1BR0UgU0VDVElPTiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYWdhemluZS1jYXJkLWltYWdlXCI+XG4gICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWltYWdlLWJnXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybCgke3Bvc3QuaW1hZ2VVcmwgfHwgJy9pbWFnZXMvcGxhY2Vob2xkZXIvaW1hZ2UuanBnJ30pYCxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZFNpemU6ICdjb3ZlcicsXG4gICAgICAgICAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogJ2NlbnRlcicsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFnYXppbmUtaW1hZ2Utb3ZlcmxheVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtYWdhemluZS1jYXRlZ29yeVwiPlxuICAgICAgICAgICAgICAgIHtwb3N0LmNhdGVnb3J5IHx8ICdaYXBpc2tpIHogcG9kcsOzxbx5J31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDT05URU5UIFNFQ1RJT04gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFnYXppbmUtY2FyZC1jb250ZW50XCI+XG4gICAgICAgICAgPGgzIGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWNhcmQtdGl0bGVcIj5cbiAgICAgICAgICAgIHtwb3N0LnRpdGxlIHx8ICdCZXogdHl0dcWCdSd9XG4gICAgICAgICAgPC9oMz5cblxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWNhcmQtZXhjZXJwdFwiPlxuICAgICAgICAgICAge3Bvc3QuZXhjZXJwdCB8fCAnJ31cbiAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWNhcmQtZm9vdGVyXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtYWdhemluZS1yZWFkLW1vcmVcIj5Denl0YWogd2nEmWNlajwvc3Bhbj5cbiAgICAgICAgICAgIHtwb3N0LnJlYWRUaW1lICYmIChcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWFnYXppbmUtcmVhZC10aW1lXCI+e3Bvc3QucmVhZFRpbWV9IG1pbjwvc3Bhbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9MaW5rPlxuICAgIDwvYXJ0aWNsZT5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJsb2dQYWdlQ2xpZW50Q29udGVudCh7IHBvc3RzID0gW10gfSkge1xuICBjb25zdCBbc2VsZWN0ZWRDYXRlZ29yeSwgc2V0U2VsZWN0ZWRDYXRlZ29yeV0gPSB1c2VTdGF0ZSgnd3N6eXN0a2llJyk7XG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcblxuICBjb25zdCBtZW1vaXplZFBvc3RzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHBvc3RzKSkgcmV0dXJuIFtdO1xuICAgIHJldHVybiBwb3N0cy5maWx0ZXIocG9zdCA9PiBwb3N0ICYmIHR5cGVvZiBwb3N0ID09PSAnb2JqZWN0Jyk7XG4gIH0sIFtwb3N0c10pO1xuXG4gIC8vIEdldCB1bmlxdWUgY2F0ZWdvcmllc1xuICBjb25zdCBjYXRlZ29yaWVzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgY2F0cyA9IFsnd3N6eXN0a2llJywgLi4ubmV3IFNldChtZW1vaXplZFBvc3RzLm1hcChwb3N0ID0+IHBvc3QuY2F0ZWdvcnkpLmZpbHRlcihCb29sZWFuKSldO1xuICAgIHJldHVybiBjYXRzO1xuICB9LCBbbWVtb2l6ZWRQb3N0c10pO1xuXG4gIC8vIEZpbHRlciBwb3N0cyBiYXNlZCBvbiBjYXRlZ29yeSBhbmQgc2VhcmNoXG4gIGNvbnN0IGZpbHRlcmVkUG9zdHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBsZXQgZmlsdGVyZWQgPSBtZW1vaXplZFBvc3RzO1xuXG4gICAgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgIT09ICd3c3p5c3RraWUnKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihwb3N0ID0+IHBvc3QuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnkpO1xuICAgIH1cblxuICAgIGlmIChzZWFyY2hUZXJtKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihwb3N0ID0+XG4gICAgICAgIHBvc3QudGl0bGU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICBwb3N0LmV4Y2VycHQ/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICBwb3N0LnRhZ3M/LnNvbWUodGFnID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkpXG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiBmaWx0ZXJlZDtcbiAgfSwgW21lbW9pemVkUG9zdHMsIHNlbGVjdGVkQ2F0ZWdvcnksIHNlYXJjaFRlcm1dKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2FuY3R1YXJ5IG1pbi1oLXNjcmVlblwiPlxuICAgICAgey8qIEhFUk8gU0VDVElPTiAtIEVuaGFuY2VkIHdpdGggU2VhcmNoICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWFnYXppbmUtaGVyb1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWhlcm8tY29udGVudFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFnYXppbmUtaGVhZGVyLWxpbmVcIj48L2Rpdj5cblxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJtYWdhemluZS10aXRsZVwiPlxuICAgICAgICAgICAgWmFwaXNraSB6IFBvZHLDs8W8eVxuICAgICAgICAgIDwvaDE+XG5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYWdhemluZS1zdWJ0aXRsZVwiPlxuICAgICAgICAgICAgSGlzdG9yaWUgbmFwaXNhbmUgc2VyY2VtIOKAoiBJbnNwaXJhY2plIHogQXpqaSDigKIgUHJha3R5a2Egam9naVxuICAgICAgICAgIDwvcD5cblxuICAgICAgICAgIHsvKiBTZWFyY2ggQmFyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOCBtYXgtdy1tZCBteC1hdXRvXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN6dWthaiBhcnR5a3XFgsOzdy4uLlwiXG4gICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1lbnRlcnByaXNlLWJyb3duLzIwIHJvdW5kZWQtbm9uZSB0ZXh0LWNoYXJjb2FsIHBsYWNlaG9sZGVyLWNoYXJjb2FsLzYwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItZW50ZXJwcmlzZS1icm93bi80MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFnYXppbmUtbWV0YVwiPlxuICAgICAgICAgICAge2ZpbHRlcmVkUG9zdHMubGVuZ3RofSB7ZmlsdGVyZWRQb3N0cy5sZW5ndGggPT09IDEgPyAnYXJ0eWt1xYInIDogJ2FydHlrdcWCw7N3J31cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWFnYXppbmUtaGVhZGVyLWxpbmVcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBDQVRFR09SWSBGSUxURVIgKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS04IGJnLXdoaXRlLzUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNnhsIG14LWF1dG8gcHgtaGVyby1wYWRkaW5nXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBqdXN0aWZ5LWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAge2NhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtjYXRlZ29yeX1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZENhdGVnb3J5KGNhdGVnb3J5KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTIgdGV4dC1zbSBmb250LWxpZ2h0IHRyYWNraW5nLXdpZGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZENhdGVnb3J5ID09PSBjYXRlZ29yeVxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1lbnRlcnByaXNlLWJyb3duIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLXdoaXRlLzgwIHRleHQtY2hhcmNvYWwgaG92ZXI6YmctZW50ZXJwcmlzZS1icm93bi8xMCBib3JkZXIgYm9yZGVyLWVudGVycHJpc2UtYnJvd24vMjAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQkxPRyBQT1NUUyAtIEVuaGFuY2VkIExheW91dCAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cIm1hZ2F6aW5lLWNvbnRlbnRcIj5cbiAgICAgICAge2ZpbHRlcmVkUG9zdHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgey8qIEZlYXR1cmVkIEFydGljbGUgLSBMYXJnZSAqL31cbiAgICAgICAgICAgIHtmaWx0ZXJlZFBvc3RzWzBdICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBweC1oZXJvLXBhZGRpbmcgbWItMTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTEyIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvcmRlci0yIGxnOm9yZGVyLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctZW50ZXJwcmlzZS1icm93bi8xMCB0ZXh0LWVudGVycHJpc2UtYnJvd24gdGV4dC14cyBmb250LW1lZGl1bSB0cmFja2luZy13aWRlIHVwcGVyY2FzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBXeXLDs8W8bmlvbnlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY2hhcmNvYWwvNjAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRQb3N0c1swXS5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBsZzp0ZXh0LTV4bCBmb250LWNvcm1vcmFudCB0ZXh0LWNoYXJjb2FsIGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZFBvc3RzWzBdLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtY2hhcmNvYWwvODAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRQb3N0c1swXS5leGNlcnB0fVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgdGV4dC1zbSB0ZXh0LWNoYXJjb2FsLzYwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57ZmlsdGVyZWRQb3N0c1swXS5hdXRob3J9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2ZpbHRlcmVkUG9zdHNbMF0ucmVhZFRpbWUgfHwgJzUnfSBtaW48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57bmV3IERhdGUoZmlsdGVyZWRQb3N0c1swXS5kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3BsLVBMJyl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvYmxvZy8ke2ZpbHRlcmVkUG9zdHNbMF0uc2x1Z31gfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtZW50ZXJwcmlzZS1icm93biBob3Zlcjp0ZXh0LWVudGVycHJpc2UtYnJvd24vODAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBDenl0YWogYXJ0eWt1xYJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj7ihpI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9yZGVyLTEgbGc6b3JkZXItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC1bNC8zXSByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gYmctY2hhcmNvYWwvNVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtmaWx0ZXJlZFBvc3RzWzBdLmltYWdlVXJsIHx8ICcvaW1hZ2VzL3BsYWNlaG9sZGVyL2ltYWdlLmpwZyd9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2ZpbHRlcmVkUG9zdHNbMF0uaW1hZ2VBbHQgfHwgZmlsdGVyZWRQb3N0c1swXS50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlciB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi03MDAgaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBBcnRpY2xlcyBHcmlkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy02eGwgbXgtYXV0byBweC1oZXJvLXBhZGRpbmdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICAgICAge2ZpbHRlcmVkUG9zdHMuc2xpY2UoMSkubWFwKChwb3N0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGFydGljbGUga2V5PXtwb3N0LnNsdWcgfHwgaW5kZXh9IGNsYXNzTmFtZT1cImdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvYmxvZy8ke3Bvc3Quc2x1Z31gfSBjbGFzc05hbWU9XCJibG9ja1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogSW1hZ2UgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC1bMTYvMTBdIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiBiZy1jaGFyY29hbC81XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17cG9zdC5pbWFnZVVybCB8fCAnL2ltYWdlcy9wbGFjZWhvbGRlci9pbWFnZS5qcGcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17cG9zdC5pbWFnZUFsdCB8fCBwb3N0LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXIgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tNTAwIGdyb3VwLWhvdmVyOnNjYWxlLTEwNVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgbGVmdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIGJnLXdoaXRlLzkwIGJhY2tkcm9wLWJsdXItc20gdGV4dC1jaGFyY29hbCB0ZXh0LXhzIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cG9zdC5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1jb3Jtb3JhbnQgdGV4dC1jaGFyY29hbCBsZWFkaW5nLXRpZ2h0IGdyb3VwLWhvdmVyOnRleHQtZW50ZXJwcmlzZS1icm93biB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cG9zdC50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNoYXJjb2FsLzcwIHRleHQtc20gbGVhZGluZy1yZWxheGVkIGxpbmUtY2xhbXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LmV4Y2VycHR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtY2hhcmNvYWwvNjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57bmV3IERhdGUocG9zdC5kYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ3BsLVBMJyl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntwb3N0LnJlYWRUaW1lIHx8ICc1J30gbWluPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICA8L2FydGljbGU+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC8+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0yeGwgbXgtYXV0byBweC1oZXJvLXBhZGRpbmcgcHktMTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWNvcm1vcmFudCB0ZXh0LWNoYXJjb2FsXCI+XG4gICAgICAgICAgICAgICAge3NlYXJjaFRlcm0gPyAnTmllIHpuYWxlemlvbm8gYXJ0eWt1xYLDs3cnIDogJ1drcsOzdGNlIHdpxJljZWogdHJlxZtjaSd9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2hhcmNvYWwvNzBcIj5cbiAgICAgICAgICAgICAgICB7c2VhcmNoVGVybVxuICAgICAgICAgICAgICAgICAgPyBgQnJhayB3eW5pa8OzdyBkbGEgXCIke3NlYXJjaFRlcm19XCIuIFNwcsOzYnVqIGlubnljaCBzxYLDs3cga2x1Y3pvd3ljaC5gXG4gICAgICAgICAgICAgICAgICA6ICdQcmFjdWplbXkgbmFkIG5vd3ltaSBpbnNwaXJ1asSFY3ltaSBhcnR5a3XFgmFtaSdcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAge3NlYXJjaFRlcm0gJiYgKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlYXJjaFRlcm0oJycpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1lbnRlcnByaXNlLWJyb3duIGhvdmVyOnRleHQtZW50ZXJwcmlzZS1icm93bi84MCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIFd5Y3p5xZvEhyB3eXN6dWtpd2FuaWVcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQ09NTVVOSVRZIFNFQ1RJT04gLSBCQUtBU0FOQSBTdGFuZGFyZHMgKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBzcGFjZS15LWxnIG1heC13LTN4bCBteC1hdXRvXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LW1kXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwic2VjdGlvbi1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgQsSFZMW6IG5hIGJpZcW8xIVjb1xuICAgICAgICAgICAgPC9oMz5cblxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYm9keS10ZXh0IG9wYWNpdHktODBcIj5cbiAgICAgICAgICAgICAgT3RyenltdWogbmFqbm93c3plIGFydHlrdcWCeSBpIGluc3BpcmFjamUgeiBkdWNob3d5Y2ggcG9kcsOzxbx5XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU0FDUkVEIERJVklERVIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteS0xMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtc20gdGV4dC1jaGFyY29hbC1nb2xkLzYwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLXB4IGJnLWNoYXJjb2FsLWdvbGQvMzBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBvcGFjaXR5LTYwXCI+4KWQPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC1weCBiZy1jaGFyY29hbC1nb2xkLzMwXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDT05UQUNUIExJTktTIC0gR2hvc3QgYnV0dG9ucyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLWxnIGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgaHJlZj1cImh0dHBzOi8vd3d3Lmluc3RhZ3JhbS5jb20vZmx5X3dpdGhfYmFrYXNhbmFcIlxuICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLWdob3N0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgSW5zdGFncmFtXG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8YVxuICAgICAgICAgICAgICBocmVmPVwibWFpbHRvOmtvbnRha3RAYmFrYXNhbmEucGxcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tZ2hvc3RcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBFbWFpbFxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC04XCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc3RvbmUgZm9udC1saWdodCBpdGFsaWMgdHJhY2tpbmctd2lkZVwiPlxuICAgICAgICAgICAgICBcIkthxbxkYSBoaXN0b3JpYSBtYSBzd29qxIUgbW9jLi4uXCJcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1jaGFyY29hbC1nb2xkIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSB1cHBlcmNhc2UgbXQtMlwiPlxuICAgICAgICAgICAgICBPbSBTd2FzdGlhc3R1XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJMaW5rIiwiSW1hZ2UiLCJSZWFjdCIsInVzZU1lbW8iLCJ1c2VTdGF0ZSIsIkJsb2dTdGF0cyIsIkJsb2dOZXdzbGV0dGVyIiwiUG9zdENhcmQiLCJwb3N0IiwiZmVhdHVyZWQiLCJjbGFzc05hbWUiLCJjYXJkQ2xhc3MiLCJhcnRpY2xlIiwiaHJlZiIsInNsdWciLCJkaXYiLCJzdHlsZSIsImJhY2tncm91bmRJbWFnZSIsImltYWdlVXJsIiwiYmFja2dyb3VuZFNpemUiLCJiYWNrZ3JvdW5kUG9zaXRpb24iLCJzcGFuIiwiY2F0ZWdvcnkiLCJoMyIsInRpdGxlIiwicCIsImV4Y2VycHQiLCJyZWFkVGltZSIsIkJsb2dQYWdlQ2xpZW50Q29udGVudCIsInBvc3RzIiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsIm1lbW9pemVkUG9zdHMiLCJBcnJheSIsImlzQXJyYXkiLCJmaWx0ZXIiLCJjYXRlZ29yaWVzIiwiY2F0cyIsIlNldCIsIm1hcCIsIkJvb2xlYW4iLCJmaWx0ZXJlZFBvc3RzIiwiZmlsdGVyZWQiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwidGFncyIsInNvbWUiLCJ0YWciLCJzZWN0aW9uIiwiaDEiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwibGVuZ3RoIiwiYnV0dG9uIiwib25DbGljayIsImgyIiwiYXV0aG9yIiwiRGF0ZSIsImRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJzcmMiLCJhbHQiLCJpbWFnZUFsdCIsImZpbGwiLCJzbGljZSIsImluZGV4IiwiYSIsInJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/blog/BlogNewsletter.jsx":
/*!************************************************!*\
  !*** ./src/components/blog/BlogNewsletter.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogNewsletter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction BlogNewsletter() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Here you would typically send the email to your newsletter service\n        console.log('Newsletter signup:', email);\n        setIsSubmitted(true);\n        setEmail('');\n        // Reset after 3 seconds\n        setTimeout(()=>setIsSubmitted(false), 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-r from-enterprise-brown/5 to-terra/5 py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-hero-padding text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl font-cormorant text-charcoal\",\n                                children: \"Bądź na bieżąco z naszymi podr\\xf3żami\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-charcoal/70 max-w-2xl mx-auto\",\n                                children: \"Otrzymuj najnowsze artykuły, inspiracje z podr\\xf3ży i ekskluzywne informacje o nadchodzących retreatach.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Tw\\xf3j adres email\",\n                                        required: true,\n                                        className: \"flex-1 px-4 py-3 bg-white border border-enterprise-brown/20 text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"px-6 py-3 bg-enterprise-brown text-white hover:bg-enterprise-brown/90 transition-all duration-300 font-medium\",\n                                        children: \"Zapisz się\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-charcoal/60 mt-3\",\n                                children: \"Nie wysyłamy spamu. Możesz się wypisać w każdej chwili.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-md mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"✓\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Dziękujemy! Sprawdź swoją skrzynkę email.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                            lineNumber: 57,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center gap-6 text-sm text-charcoal/60\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCE7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Cotygodniowy newsletter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83C\\uDF81\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Ekskluzywne treści\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDEAB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Bez spamu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogNewsletter.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogNewsletter, \"wtA9wJPvsrhILA4gWwZ7+/0vC5A=\");\n_c = BlogNewsletter;\nvar _c;\n$RefreshReg$(_c, \"BlogNewsletter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/blog/BlogNewsletter.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/blog/BlogStats.jsx":
/*!*******************************************!*\
  !*** ./src/components/blog/BlogStats.jsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BlogStats(param) {\n    let { posts } = param;\n    const totalPosts = posts.length;\n    const categories = [\n        ...new Set(posts.map((post)=>post.category).filter(Boolean))\n    ];\n    const totalReadTime = posts.reduce((total, post)=>total + (parseInt(post.readTime) || 5), 0);\n    const stats = [\n        {\n            label: 'Artykułów',\n            value: totalPosts,\n            icon: '📝'\n        },\n        {\n            label: 'Kategorii',\n            value: categories.length,\n            icon: '🏷️'\n        },\n        {\n            label: 'Minut czytania',\n            value: totalReadTime,\n            icon: '⏱️'\n        },\n        {\n            label: 'Krajów',\n            value: '2+',\n            icon: '🌏'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white/50 backdrop-blur-sm py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-hero-padding\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl mb-2\",\n                                children: stat.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-cormorant text-charcoal font-light\",\n                                children: stat.value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n                                lineNumber: 40,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-charcoal/70 font-light\",\n                                children: stat.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n                                lineNumber: 43,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\blog\\\\BlogStats.jsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c = BlogStats;\nvar _c;\n$RefreshReg$(_c, \"BlogStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/blog/BlogStats.jsx\n"));

/***/ })

});