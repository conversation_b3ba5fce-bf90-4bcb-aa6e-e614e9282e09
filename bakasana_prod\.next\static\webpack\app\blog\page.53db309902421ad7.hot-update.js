"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx":
/*!************************************************!*\
  !*** ./src/app/blog/BlogPageClientContent.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPageClientContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Magazine Style Blog Card\nconst PostCard = (param)=>{\n    let { post, featured = false, className = '' } = param;\n    if (!post) return null;\n    const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        className: \"\".concat(cardClass, \" \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: \"/blog/\".concat(post.slug || '#'),\n            className: \"magazine-card-link\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-image-bg\",\n                        style: {\n                            backgroundImage: \"url(\".concat(post.imageUrl || '/images/placeholder/image.jpg', \")\"),\n                            backgroundSize: 'cover',\n                            backgroundPosition: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-image-overlay\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"magazine-category\",\n                                children: post.category || 'Zapiski z podróży'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-card-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"magazine-card-title\",\n                            children: post.title || 'Bez tytułu'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-card-excerpt\",\n                            children: post.excerpt || ''\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-card-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-more\",\n                                    children: \"Czytaj więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                post.readTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"magazine-read-time\",\n                                    children: [\n                                        post.readTime,\n                                        \" min\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PostCard;\nfunction BlogPageClientContent(param) {\n    let { posts = [] } = param;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('wszystkie');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('');\n    const memoizedPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[memoizedPosts]\": ()=>{\n            if (!Array.isArray(posts)) return [];\n            return posts.filter({\n                \"BlogPageClientContent.useMemo[memoizedPosts]\": (post)=>post && typeof post === 'object'\n            }[\"BlogPageClientContent.useMemo[memoizedPosts]\"]);\n        }\n    }[\"BlogPageClientContent.useMemo[memoizedPosts]\"], [\n        posts\n    ]);\n    // Get unique categories\n    const categories = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[categories]\": ()=>{\n            const cats = [\n                'wszystkie',\n                ...new Set(memoizedPosts.map({\n                    \"BlogPageClientContent.useMemo[categories]\": (post)=>post.category\n                }[\"BlogPageClientContent.useMemo[categories]\"]).filter(Boolean))\n            ];\n            return cats;\n        }\n    }[\"BlogPageClientContent.useMemo[categories]\"], [\n        memoizedPosts\n    ]);\n    // Filter posts based on category and search\n    const filteredPosts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)({\n        \"BlogPageClientContent.useMemo[filteredPosts]\": ()=>{\n            let filtered = memoizedPosts;\n            if (selectedCategory !== 'wszystkie') {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>post.category === selectedCategory\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            if (searchTerm) {\n                filtered = filtered.filter({\n                    \"BlogPageClientContent.useMemo[filteredPosts]\": (post)=>{\n                        var _post_title, _post_excerpt, _post_tags;\n                        return ((_post_title = post.title) === null || _post_title === void 0 ? void 0 : _post_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_excerpt = post.excerpt) === null || _post_excerpt === void 0 ? void 0 : _post_excerpt.toLowerCase().includes(searchTerm.toLowerCase())) || ((_post_tags = post.tags) === null || _post_tags === void 0 ? void 0 : _post_tags.some({\n                            \"BlogPageClientContent.useMemo[filteredPosts]\": (tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())\n                        }[\"BlogPageClientContent.useMemo[filteredPosts]\"]));\n                    }\n                }[\"BlogPageClientContent.useMemo[filteredPosts]\"]);\n            }\n            return filtered;\n        }\n    }[\"BlogPageClientContent.useMemo[filteredPosts]\"], [\n        memoizedPosts,\n        selectedCategory,\n        searchTerm\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-sanctuary min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-hero\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"magazine-title\",\n                            children: \"Zapiski z Podr\\xf3ży\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"magazine-subtitle\",\n                            children: \"Historie napisane sercem • Inspiracje z Azji • Praktyka jogi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 max-w-md mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Szukaj artykuł\\xf3w...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 rounded-none text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-meta\",\n                            children: [\n                                filteredPosts.length,\n                                \" \",\n                                filteredPosts.length === 1 ? 'artykuł' : 'artykułów'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-header-line\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-8 bg-white/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-hero-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-3\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-2 text-sm font-light tracking-wide transition-all duration-300 \".concat(selectedCategory === category ? 'bg-enterprise-brown text-white' : 'bg-white/80 text-charcoal hover:bg-enterprise-brown/10 border border-enterprise-brown/20'),\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"magazine-content\",\n                children: memoizedPosts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-grid\",\n                    children: [\n                        memoizedPosts[0] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"magazine-featured\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                post: memoizedPosts[0],\n                                featured: true,\n                                className: \"featured-card\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 152,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 151,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-secondary\",\n                            children: memoizedPosts.slice(1, 3).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"magazine-secondary-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                        post: post,\n                                        className: \"secondary-card\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 19\n                                    }, this)\n                                }, \"secondary-\".concat((post === null || post === void 0 ? void 0 : post.slug) || index), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this),\n                        memoizedPosts.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"magazine-grid-small\",\n                            children: memoizedPosts.slice(3).map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"magazine-small-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostCard, {\n                                        post: post,\n                                        className: \"small-card\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 21\n                                    }, this)\n                                }, \"small-\".concat((post === null || post === void 0 ? void 0 : post.slug) || index), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"magazine-empty\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"magazine-empty-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"magazine-empty-title\",\n                                children: \"Wkr\\xf3tce więcej treści\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"magazine-empty-text\",\n                                children: \"Pracujemy nad nowymi inspirującymi artykułami\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-lg max-w-3xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"section-header\",\n                                    children: \"Bądź na bieżąco\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"body-text opacity-80\",\n                                    children: \"Otrzymuj najnowsze artykuły i inspiracje z duchowych podr\\xf3ży\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center my-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-sm text-charcoal-gold/60\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg opacity-60\",\n                                        children: \"ॐ\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-px bg-charcoal-gold/30\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-lg justify-center items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.instagram.com/fly_with_bakasana\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-ghost\",\n                                    children: \"Instagram\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"btn-ghost\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-stone font-light italic tracking-wide\",\n                                    children: '\"Każda historia ma swoją moc...\"'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2\",\n                                    children: \"Om Swastiastu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPageClientContent, \"lo2uijBVUg9EXKtYRzlq39aa0Ko=\");\n_c1 = BlogPageClientContent;\nvar _c, _c1;\n$RefreshReg$(_c, \"PostCard\");\n$RefreshReg$(_c1, \"BlogPageClientContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/BlogPageClientContent.jsx\n"));

/***/ })

});